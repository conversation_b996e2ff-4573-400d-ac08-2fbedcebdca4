import React, { useState, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import { BASE_URL } from "../../../../services/config";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import Chart from "chart.js/auto";

const parseAgeGroup = (ageGroup) => {
  if (!ageGroup) return null;
  const ageGroups = {
    'below_25': { min: 0, max: 24 },
    '26_39': { min: 25, max: 39 },
    '40_above': { min: 40, max: 100 }
  };
  return ageGroups[ageGroup] || null;
};

const convertDateFormat = (dateStr) => {
  if (!dateStr) return '';
  // Convert from dd/mm/yyyy to yyyy-mm-dd
  const parts = dateStr.split('/');
  if (parts.length === 3) {
    return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
  }
  return dateStr;
};

const DriverRatingReport = () => {
  const chartRef = useRef(null);
  const canvasRef = useRef(null);

  const [drivers, setDrivers] = useState([]);
  const [filteredDrivers, setFilteredDrivers] = useState([]);
  const [trendData, setTrendData] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  const [minAge, setMinAge] = useState('');
  const [maxAge, setMaxAge] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [driverName, setDriverName] = useState('');
  const [vehicle, setVehicle] = useState('');

  const [driverOptions, setDriverOptions] = useState([]);
  const [vehicleOptions, setVehicleOptions] = useState([]);
  const [tempDriverName, setTempDriverName] = useState('');
  const [tempVehicle, setTempVehicle] = useState('');
  const [tempMinAge, setTempMinAge] = useState('');
  const [tempMaxAge, setTempMaxAge] = useState('');
  const [tempDateFrom, setTempDateFrom] = useState('');
  const [tempDateTo, setTempDateTo] = useState('');

  const [summary, setSummary] = useState(null);
  const [pagination, setPagination] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [trendPeriod, setTrendPeriod] = useState('weekly'); // weekly or monthly

  const applyLocalFilters = useCallback(() => {
    // The backend already applies all filters, so we just set filteredDrivers to drivers
    setFilteredDrivers(drivers);
  }, [drivers]);

  const fetchData = useCallback(async (page = 1) => {
    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Authentication required");

      const response = await axios.get(`${BASE_URL}api/drivers/performance_report/`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
        params: {
          page,
          page_size: pageSize,
          driver_name: driverName,
          vehicle: vehicle,
          min_age: minAge,
          max_age: maxAge,
          start_date: convertDateFormat(dateFrom),
          end_date: convertDateFormat(dateTo),
        }
      });

      const { drivers: apiDrivers, summary, pagination } = response.data;

      const processedDrivers = apiDrivers.map((d) => ({
        name: d.driver_name,
        vehicle: d.vehicle,
        age: d.age_group,
        rating: d.current_rating,
        paymentDays: d.payment_days,
        onTime: d.on_time_payments,
        late: d.late_payments,
        missed: d.missed_payments,
        status: d.status,
        created_at: d.created_at,
        driverCode: d.driver_code,
        successRate: d.success_rate,
        vehicleCount: d.vehicle_count
      }));

      const uniqueDrivers = [...new Set(processedDrivers.map(d => d.name))];
      const uniqueVehicles = [...new Set(processedDrivers.map(d => d.vehicle))];
      setDriverOptions(uniqueDrivers);
      setVehicleOptions(uniqueVehicles);

      setDrivers(processedDrivers);
      setFilteredDrivers(processedDrivers);
      setSummary(summary);
      setPagination(pagination);
      setCurrentPage(pagination.current_page);
      setIsLoading(false);
    } catch (err) {
      console.error("Failed to fetch drivers:", err);
      setIsLoading(false);
    }
  }, [pageSize, driverName, vehicle, minAge, maxAge, dateFrom, dateTo]);

  const fetchTrendData = useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Authentication required");

      // Build query parameters from current filters
      const params = new URLSearchParams();
      if (driverName) params.append('driver_name', driverName);
      if (vehicle) params.append('vehicle', vehicle);
      if (minAge) params.append('min_age', minAge);
      if (maxAge) params.append('max_age', maxAge);
      if (dateFrom) params.append('start_date', convertDateFormat(dateFrom));
      if (dateTo) params.append('end_date', convertDateFormat(dateTo));
      params.append('period', trendPeriod);

      const response = await axios.get(`${BASE_URL}api/drivers/rating_trends/?${params.toString()}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        }
      });

      return response.data.trends_data || {};
    } catch (err) {
      console.error(`Failed to fetch trend data:`, err);
      return {};
    }
  }, [driverName, vehicle, minAge, maxAge, dateFrom, dateTo, trendPeriod]);

  useEffect(() => {
    fetchData(currentPage);
  }, [currentPage, fetchData]);

  useEffect(() => {
    applyLocalFilters();
  }, [applyLocalFilters]);

  useEffect(() => {
    if (!filteredDrivers.length) {
      setTrendData({});
      return;
    }

    const fetchAllTrendData = async () => {
      try {
        const trendsData = await fetchTrendData();
        setTrendData(trendsData);
      } catch (error) {
        console.error("Error fetching trend data:", error);
        setTrendData({});
      }
    };

    fetchAllTrendData();
  }, [filteredDrivers, fetchTrendData]);

  useEffect(() => {
    if (!canvasRef.current || Object.keys(trendData).length === 0) {
      return;
    }
    const ctx = canvasRef.current.getContext("2d");

    if (chartRef.current) chartRef.current.destroy();

    // Get all unique week labels across all drivers
    const allWeeks = Array.from(
      new Set(
        Object.values(trendData).flatMap(data =>
          data.map(d => d.week_label || d.month_label)
        )
      )
    ).sort((a, b) => {
      const weekA = parseInt(a.match(/\d+/)[0]);
      const weekB = parseInt(b.match(/\d+/)[0]);
      return weekA - weekB;
    });

    // Create datasets for each driver
    const datasets = Object.entries(trendData).map(([driverName, data], index) => {
      const ratings = allWeeks.map(week => {
        const weekData = data.find(d => d.week_label === week || d.month_label === week);
        return weekData ? weekData.rating : null;
      });

      return {
        label: driverName,
        data: ratings,
        borderColor: `hsl(${index * 137.5 % 360}, 70%, 50%)`,
        backgroundColor: `hsla(${index * 137.5 % 360}, 70%, 50%, 0.1)`,
        fill: false,
        tension: 0.4,
        pointRadius: 5,
        pointHoverRadius: 7,
        hidden: false,
      };
    });

    chartRef.current = new Chart(ctx, {
      type: "line",
      data: {
        labels: allWeeks,
        datasets,
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            max: 5,
            title: {
              display: true,
              text: "Rating",
            },
          },
          x: {
            title: {
              display: true,
              text: trendPeriod === 'weekly' ? "Week" : "Month",
            },
          },
        },
        plugins: {
          legend: {
            display: true,
            position: "top",
            labels: {
              boxWidth: 20,
              padding: 15,
            },
          },
          tooltip: {
            callbacks: {
              label: (context) => `Rating: ${context.parsed.y || 'N/A'}`,
              afterLabel: (context) => {
                const driverName = context.dataset.label;
                const week = context.label;
                const data = trendData[driverName]?.find(d => d.week_label === week || d.month_label === week);
                return data ? `Payments Made: ${data.payments_made}` : '';
              },
            },
          },
        },
      },
    });

    return () => {
      if (chartRef.current) chartRef.current.destroy();
    };
  }, [trendData, trendPeriod]);

  const applyFilters = () => {
    setDriverName(tempDriverName);
    setVehicle(tempVehicle);
    setMinAge(tempMinAge);
    setMaxAge(tempMaxAge);
    setDateFrom(tempDateFrom);
    setDateTo(tempDateTo);
    setCurrentPage(1);
    // Trigger immediate data fetch with new filters
    fetchData(1);
  };

  const clearFilters = () => {
    setTempDriverName('');
    setTempVehicle('');
    setTempMinAge('');
    setTempMaxAge('');
    setTempDateFrom('');
    setTempDateTo('');

    setDriverName('');
    setVehicle('');
    setMinAge('');
    setMaxAge('');
    setDateFrom('');
    setDateTo('');

    setCurrentPage(1);
    // Trigger immediate data fetch with cleared filters
    fetchData(1);
  };

  const exportReport = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem("token");
      if (!token) {
        alert("Authentication token not found");
        return;
      }

      const params = new URLSearchParams();
      if (tempDriverName) params.append('driver_name', tempDriverName);
      if (tempVehicle) params.append('vehicle', tempVehicle);
      if (tempMinAge) params.append('min_age', tempMinAge);
      if (tempMaxAge) params.append('max_age', tempMaxAge);
      if (tempDateFrom) params.append('start_date', convertDateFormat(tempDateFrom));
      if (tempDateTo) params.append('end_date', convertDateFormat(tempDateTo));

      let allDrivers = [];
      let currentPage = 1;
      let hasMorePages = true;
      const pageSize = 100;

      while (hasMorePages) {
        const pageParams = new URLSearchParams(params);
        pageParams.append('page', currentPage.toString());
        pageParams.append('page_size', pageSize.toString());

        const response = await axios.get(`${BASE_URL}api/drivers/performance_report/?${pageParams.toString()}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        const pageData = response.data.drivers || [];
        allDrivers = [...allDrivers, ...pageData];
        const totalPages = response.data.pagination.total_pages || 1;
        hasMorePages = currentPage < totalPages;
        currentPage++;
      }

      if (!allDrivers.length) {
        alert("No data available for export.");
        return;
      }

      const doc = new jsPDF("landscape", "pt", "A4");

      doc.setFontSize(20);
      doc.setTextColor(255, 102, 0);
      doc.text("KadereConnect", 40, 40);

      doc.setFontSize(16);
      doc.setTextColor(0, 0, 0);
      doc.text("Driver Rating Report", 40, 65);

      doc.setFontSize(10);
      doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 40, 80);

      if (summary) {
        doc.setFontSize(12);
        doc.text("Summary:", 40, 100);
        doc.setFontSize(10);
        doc.text(`Total Drivers: ${summary.total_drivers}`, 40, 115);
        doc.text(`Active Drivers: ${summary.active_drivers}`, 180, 115);
        doc.text(`Average Rating: ${summary.average_rating.toFixed(1)}`, 320, 115);
        doc.text(`Total Points: ${summary.total_points_all_drivers}`, 460, 115);
      }

      const head = [["Driver Name", "Vehicle", "Rating", "Payment Days", "On Time", "Late", "Missed", "Status"]];
      const body = allDrivers.map(d => [
        d.driver_name,
        d.vehicle || 'N/A',
        d.current_rating.toFixed(1),
        d.payment_days,
        d.on_time_payments,
        d.late_payments,
        d.missed_payments,
        d.status
      ]);

      autoTable(doc, {
        head,
        body,
        startY: summary ? 145 : 100,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [255, 102, 0] },
      });

      const pageHeight = doc.internal.pageSize.height;
      doc.setFontSize(8);
      doc.setTextColor(128, 128, 128);
      doc.text("Generated by KadereConnect - Vehicle Management System", 40, pageHeight - 20);

      doc.save(`driver_rating_report_${new Date().toISOString().slice(0, 10)}.pdf`);

    } catch (error) {
      console.error("PDF Export Error:", error);
      alert("Failed to export PDF. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getRatingColor = (rating) => {
    if (rating >= 4.5) return "text-green-600 font-semibold";
    if (rating >= 3.0) return "text-yellow-600 font-semibold";
    return "text-red-600 font-semibold";
  };

  const getStatusBadge = (status) =>
    status === "Active"
      ? <span className="text-green-700 bg-green-100 px-2 py-1 rounded-full text-xs">Active</span>
      : <span className="text-red-700 bg-red-100 px-2 py-1 rounded-full text-xs">Inactive</span>;

  const handlePreviousPage = () => {
    if (pagination?.has_previous) setCurrentPage(prev => prev - 1);
  };

  const handleNextPage = () => {
    if (pagination?.has_next) setCurrentPage(prev => prev + 1);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8 font-serif">
      <div className="bg-orange-600 text-white rounded-2xl shadow-lg p-6 mb-6">
        <h1 className="text-3xl font-bold mb-1">Driver Rating Report</h1>
        <p className="text-sm opacity-90">Visual breakdown of driver ratings with trend analysis</p>
      </div>

      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-xl shadow p-4">
            <h3 className="text-gray-500 text-sm">Total Drivers</h3>
            <p className="text-2xl font-bold">{summary.total_drivers}</p>
          </div>
          <div className="bg-white rounded-xl shadow p-4">
            <h3 className="text-gray-500 text-sm">Active Drivers</h3>
            <p className="text-2xl font-bold text-green-600">{summary.active_drivers}</p>
          </div>
          <div className="bg-white rounded-xl shadow p-4">
            <h3 className="text-gray-500 text-sm">Average Rating</h3>
            <p className="text-2xl font-bold text-orange-600">{summary.average_rating.toFixed(1)}</p>
          </div>
          <div className="bg-white rounded-xl shadow p-4">
            <h3 className="text-gray-500 text-sm">Total Points</h3>
            <p className="text-2xl font-bold text-blue-600">{summary.total_points_all_drivers}</p>
          </div>
        </div>
      )}

      <div className="bg-white rounded-2xl shadow p-6 mb-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
          <select
            value={tempDriverName}
            onChange={(e) => setTempDriverName(e.target.value)}
            className="border rounded px-2 py-1"
          >
            <option value="">All Drivers</option>
            {driverOptions.map((name, i) => (
              <option key={i} value={name}>{name}</option>
            ))}
          </select>

          <select
            value={tempVehicle}
            onChange={(e) => setTempVehicle(e.target.value)}
            className="border rounded px-2 py-1"
          >
            <option value="">All Vehicles</option>
            {vehicleOptions.map((v, i) => (
              <option key={i} value={v}>{v}</option>
            ))}
          </select>

          <input
            type="number"
            placeholder="Min Age"
            value={tempMinAge}
            onChange={(e) => setTempMinAge(e.target.value)}
            className="border rounded px-2 py-1"
          />
          <input
            type="number"
            placeholder="Max Age"
            value={tempMaxAge}
            onChange={(e) => setTempMaxAge(e.target.value)}
            className="border rounded px-2 py-1"
          />
          <input
            type="date"
            value={tempDateFrom}
            onChange={(e) => setTempDateFrom(e.target.value)}
            className="border rounded px-2 py-1"
          />
          <input
            type="date"
            value={tempDateTo}
            onChange={(e) => setTempDateTo(e.target.value)}
            className="border rounded px-2 py-1"
          />
        </div>

        <div className="flex gap-3">
          <button onClick={applyFilters} className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700">Apply Filters</button>
          <button onClick={clearFilters} className="bg-gray-200 px-4 py-2 rounded hover:bg-gray-300">Clear</button>
          <button onClick={exportReport} className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-auto">Export Report</button>
        </div>

        {/* Trend Period Filter */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">Rating Trend Period</label>
          <select
            value={trendPeriod}
            onChange={(e) => setTrendPeriod(e.target.value)}
            className="border rounded px-3 py-2 w-48"
          >
            <option value="weekly">Weekly Trend</option>
            <option value="monthly">Monthly Trend</option>
          </select>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center text-orange-500 font-medium py-10">Loading report...</div>
      ) : (
        <>
          <div className="bg-white rounded-2xl shadow p-6 mb-8">
            <h2 className="text-xl font-semibold text-orange-600 mb-4">
              Rating Trends for {driverName ? driverName : 'All Filtered Drivers'} ({trendPeriod === 'weekly' ? 'Weekly' : 'Monthly'})
            </h2>
            {filteredDrivers.length === 0 ? (
              <div className="text-center text-gray-500 font-medium py-10">
                No data found based on your filters.
              </div>
            ) : Object.keys(trendData).length === 0 ? (
              <div className="text-center text-gray-500 font-medium py-10">
                No trend data available for the selected drivers.
              </div>
            ) : (
              <canvas id="ratingTrendChart" ref={canvasRef} height="100"></canvas>
            )}
          </div>

          <div className="bg-white rounded-2xl shadow p-6">
            <h2 className="text-xl font-semibold text-orange-600 mb-4">Ratings Report Table</h2>
            {filteredDrivers.length === 0 ? (
              <div className="text-center text-gray-500 font-medium py-10">
                No data found based on your filters.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full table-auto border border-gray-200 text-sm">
                  <thead className="bg-orange-100 text-center">
                    <tr>
                      <th className="px-4 py-2 border">Driver Name</th>
                      <th className="px-4 py-2 border">Vehicle</th>
                      <th className="px-4 py-2 border">Rating</th>
                      <th className="px-4 py-2 border">Payment Days</th>
                      <th className="px-4 py-2 border">On Time</th>
                      <th className="px-4 py-2 border">Late</th>
                      <th className="px-4 py-2 border">Missed</th>
                      <th className="px-4 py-2 border">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredDrivers.map((d, i) => (
                      <tr key={i} className="hover:bg-orange-50">
                        <td className="px-4 py-2 border">{d.name}</td>
                        <td className="px-4 py-2 border">{d.vehicle}</td>
                        <td className={`px-4 py-2 border ${getRatingColor(d.rating)}`}>{d.rating}</td>
                        <td className="px-4 py-2 border">{d.paymentDays}</td>
                        <td className="px-4 py-2 border">{d.onTime}</td>
                        <td className="px-4 py-2 border">{d.late}</td>
                        <td className="px-4 py-2 border">{d.missed}</td>
                        <td className="px-4 py-2 border">{getStatusBadge(d.status)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </>
      )}

      {pagination && (
        <div className="mt-4 flex justify-center gap-2">
          <button 
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            onClick={handlePreviousPage}
            disabled={!pagination.has_previous}
          >
            Previous
          </button>
          <span className="px-4 py-2">
            Page {pagination.current_page} of {pagination.total_pages}
          </span>
          <button 
            className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50"
            onClick={handleNextPage}
            disabled={!pagination.has_next}
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

export default DriverRatingReport;