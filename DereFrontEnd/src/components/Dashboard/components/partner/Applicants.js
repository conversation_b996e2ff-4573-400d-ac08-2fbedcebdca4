import React, { useState } from "react";
import { Dialog } from "@headlessui/react";
import { FiLink, FiStar } from "react-icons/fi";
import FileViewerModal from "../common/FileViewerModal";
import axios from "axios";
import { BASE_URL } from "../../../../services/config";

const Applicants = ({
  jobApplicants,
  loading,
  getWorkAreaName,
  getVehicleTypeName,
  AcceptingApplicationId,
  removingApplicationId,
  handleRemove,
  handleAccept,
}) => {
  const [isSuccessOpen, setIsSuccessOpen] = useState(false);
  const [isRejectionOpen, setIsRejectionOpen] = useState(false);
  const [isAlreadyHiredOpen, setIsAlreadyHiredOpen] = useState(false);
  const [isFileViewerOpen, setIsFileViewerOpen] = useState(false);
  const [isRatingOpen, setIsRatingOpen] = useState(false);
  const [currentFileUrl, setCurrentFileUrl] = useState('');
  const [currentFileName, setCurrentFileName] = useState('');
  const [currentFileType, setCurrentFileType] = useState('');
  const [currentApplicantName, setCurrentApplicantName] = useState('');
  const [currentRating, setCurrentRating] = useState(null);
  const [isRatingLoading, setIsRatingLoading] = useState(false);
  const [ratingError, setRatingError] = useState(null);

  const handleViewFile = (fileUrl, fileName, fileType, applicantName) => {
    setCurrentFileUrl(fileUrl);
    setCurrentFileName(fileName);
    setCurrentFileType(fileType);
    setCurrentApplicantName(applicantName);
    setIsFileViewerOpen(true);
  };

  const handleViewRating = async (driverId) => {
    setIsRatingLoading(true);
    setIsRatingOpen(true);
    setRatingError(null);
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        throw new Error("Authentication token not found.");
      }
      const response = await axios.get(`${BASE_URL}api/drivers/${driverId}/rating/`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
      });
      console.log("Applicants Rating API Response:", response.data);
      setCurrentRating(response.data);
    } catch (error) {
      console.error("Error fetching driver rating:", error);
      const errorMessage = error.response?.data?.detail || error.message || 'Failed to fetch rating data';
      setRatingError(errorMessage);
      setCurrentRating(null);
    } finally {
      setIsRatingLoading(false);
    }
  };

  const handleAcceptWithDialog = async (applicationId) => {
    try {
      const result = await handleAccept(applicationId);
      // console.log("Response in Applicants:", result);

      if (result && result.success) {
        setIsSuccessOpen(true);
      } else {
        setIsAlreadyHiredOpen(true);
      }
    } catch (error) {
      console.error("Error in handleAcceptWithDialog:", error);
      setIsAlreadyHiredOpen(true);
    }
  };

  const handleRemoveWithDialog = async (applicationId) => {
    try {
      await handleRemove(applicationId);
      setIsRejectionOpen(true);
    } catch (error) {
      console.error("Error rejecting application:", error);
    }
  };

  return (
    <div className="bg-white font-serif bg-opacity-60 rounded-xl mb-8 mt-2 shadow-lg py-4 h-[80vh] md:h-[60vh] w-[90%] flex flex-col items-start justify-start overflow-y-auto custom-scrollbar">
      {/* Success Dialog */}
      <Dialog
        open={isSuccessOpen}
        onClose={() => setIsSuccessOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto w-full max-w-md rounded-lg bg-white">
            <div className="p-8">
              <div className="flex items-center justify-center mb-4">
                <svg
                  className="w-12 h-12 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <Dialog.Title className="text-2xl font-bold text-center text-gray-800 mb-4">
                Driver Hired!
              </Dialog.Title>
              <p className="text-center text-gray-600 mb-6">
                Your driver's details have been successfully moved to the My
                Drivers tab for better management.
              </p>
              <div className="flex justify-center">
                <button
                  onClick={() => setIsSuccessOpen(false)}
                  className="bg-green-600 text-white py-2 px-6 rounded-md hover:bg-green-700 transition-colors"
                >
                  View My Drivers
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Rejection Dialog */}
      <Dialog
        open={isRejectionOpen}
        onClose={() => setIsRejectionOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto w-full max-w-md rounded-lg bg-white">
            <div className="p-8">
              <div className="flex items-center justify-center mb-4">
                <svg
                  className="w-12 h-12 text-red-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <Dialog.Title className="text-2xl font-bold text-center text-gray-800 mb-4">
                Application Rejected
              </Dialog.Title>
              <p className="text-center text-gray-600 mb-6">
                The application has been rejected successfully.
              </p>
              <div className="flex justify-center">
                <button
                  onClick={() => setIsRejectionOpen(false)}
                  className="bg-red-600 text-white py-2 px-6 rounded-md hover:bg-red-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
      {/* Already Hired Dialog */}
      <Dialog
        open={isAlreadyHiredOpen}
        onClose={() => setIsAlreadyHiredOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto w-full max-w-md rounded-lg bg-white">
            <div className="p-8">
              <div className="flex items-center justify-center mb-4">
                <svg
                  className="w-12 h-12 text-yellow-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              </div>
              <Dialog.Title className="text-2xl font-bold text-center text-gray-800 mb-4">
                Driver Already Hired
              </Dialog.Title>
              <p className="text-center text-gray-600 mb-6">
                This driver has already been hired for Another job position by
                you.
              </p>
              <div className="flex justify-center">
                <button
                  onClick={() => setIsAlreadyHiredOpen(false)}
                  className="bg-yellow-600 text-white py-2 px-6 rounded-md hover:bg-yellow-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Rating Dialog */}
      <Dialog
        open={isRatingOpen}
        onClose={() => setIsRatingOpen(false)}
        className="relative z-50"
      >
        <div className="fixed font-serif inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="mx-auto  font-serif w-full max-w-lg rounded-lg bg-white">
            <div className="p-8">
              <div className="flex items-center justify-center mb-4">
                <FiStar className="w-12 h-12 text-blue-500" />
              </div>
              <Dialog.Title className="text-2xl font-bold text-center text-gray-800 mb-4">
                Driver Rating - {currentRating?.driver_name || 'Unknown'}
              </Dialog.Title>
              {isRatingLoading ? (
                <p className="text-center text-gray-600 mb-6">Loading rating...</p>
              ) : ratingError ? (
                <p className="text-center text-red-600 mb-6">
                  Error: {ratingError}. Please try again later.
                </p>
              ) : currentRating && currentRating.driver_id ? (
                <div className="text-gray-600 mb-6">
                  {/* Debug info */}
                  <div className="text-xs text-gray-500 mb-2">
                    Debug: current_rating = {JSON.stringify(currentRating.current_rating)}, 
                    type = {typeof currentRating.current_rating},
                    currentRating keys = {Object.keys(currentRating).join(', ')}
                  </div>
                  
                  {/* Status indicator */}
                  <div className={`text-sm font-medium mb-4 ${currentRating.current_rating > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    Status: {currentRating.current_rating > 0 ? 'Active' : 'Inactive'}
                  </div>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div>
                      <p className="text-lg font-semibold">Current Rating</p>
                      <p className="text-xl font-bold text-blue-600">
                        {currentRating.current_rating?.toFixed(1) || 'N/A'} / 5.0
                        <span className="ml-2 text-sm font-normal">({currentRating.rating_level || 'N/A'})</span>
                      </p>
                    </div>
                    <div>
                      <p className="text-lg font-semibold">Success Rate</p>
                      <p className="text-xl font-bold text-blue-600">{currentRating.success_rate || 0}%</p>
                    </div>
                  </div>
                  <div className="mb-6">
                    <p className="text-lg font-semibold mb-2">Payment Performance (All Time)</p>
                    <div className="grid grid-cols-3 gap-2 text-center">
                      <div>
                        <p className="text-sm">On Time</p>
                        <p className="font-bold text-green-600">{currentRating.payment_performance?.on_time || 0}</p>
                      </div>
                      <div>
                        <p className="text-sm">Late</p>
                        <p className="font-bold text-yellow-600">{currentRating.payment_performance?.late || 0}</p>
                      </div>
                      <div>
                        <p className="text-sm">Missed</p>
                        <p className="font-bold text-red-600">{currentRating.payment_performance?.missed || 0}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-600 mb-6">
                  <p>No rating data available. This driver has not recorded any payments yet.</p>
                  <p className="text-sm text-gray-500 mt-2">Status: Inactive</p>
                  <div className="mt-2 text-xs text-gray-500">
                    Debug: currentRating = {currentRating ? 'exists' : 'null'}, 
                    driver_id = {currentRating?.driver_id || 'none'},
                    keys = {currentRating ? Object.keys(currentRating).join(', ') : 'none'}
                  </div>
                </div>
              )}
              <div className="flex justify-center">
                <button
                  onClick={() => setIsRatingOpen(false)}
                  className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>

      {/* Applicants List */}
      {jobApplicants.length > 0 ? (
        jobApplicants.map((jobApplicant) => (
          <div
            key={`${jobApplicant.id}-${jobApplicant.driver.job_id}`}
            className="flex flex-col md:flex-row md:justify-center md:items-center p-4 md:py-4 xl:p-5 bg-gray-300 w-[90%] mt-4 mx-4 rounded-xl"
          >
            <div className="xl:gap-6 md:gap-4 md:w-[40%]">
              <div className="bg-gray-300 w-[90%] py-1 px-1 md:px-6 flex flex-col items-start justify-start rounded-xl gap-1 relative">
                <p className="md:text-xl text-sm xl:text-2xl text-left font-semibold font-serif mr-2">
                  JOB ID:{" "}
                  <span className="text-red-500">{jobApplicant.job.id}</span>
                </p>
                <p className="text-xs xl:text-xl font-semibold font-serif mr-2">
                  Name:{" "}
                  <span className="font-thin">
                    {jobApplicant.driver.first_name}{" "}
                    {jobApplicant.driver.last_name}
                  </span>
                </p>
                <p className="text-xs xl:text-xl text-start font-semibold font-serif mr-2">
                  Email:{" "}
                  <span className="font-thin">{jobApplicant.driver.email}</span>
                </p>
                <p className="text-xs xl:text-xl font-semibold font-serif mr-2">
                  Phone No:{" "}
                  <span className="font-thin">
                    {jobApplicant.driver.mobile_number}
                  </span>
                </p>
                <p className="text-xs xl:text-xl text-left font-semibold font-serif mr-2">
                  Vehicle Licence:{" "}
                  <span className="font-thin">
                    {getVehicleTypeName(jobApplicant.driver.vehicle_type)}
                  </span>
                </p>
                <p className="text-xs xl:text-xl text-left font-semibold font-serif mr-2">
                  Work Area:{" "}
                  <span className="font-thin">
                    {getWorkAreaName(jobApplicant.driver.work_area)}
                  </span>
                </p>
              </div>
            </div>

            {/* Documents Section */}
            <div className="flex flex-col items-start py-2 mt-1 md:w-[40%]">
              <p className="font-serif font-semibold text-sm xl:text-lg underline mb-2">
                APPLICANT DOCUMENTATION
              </p>
              <p className="font-serif font-thin xl:text-lg text-xs mb-2">
                Click link to download:
              </p>
              {/* Document links */}
              {[
                { label: "ID Card", url: jobApplicant.driver.id_photo },
                { label: "PSV PHOTO", url: jobApplicant.driver.psv_photo },
                {
                  label: "LICENSE PHOTO",
                  url: jobApplicant.driver.license_photo,
                },
                {
                  label: "CERTIFICATE OF GOOD CONDUCT",
                  url: jobApplicant.driver.good_conduct_photo,
                },
                {
                  label: "UBER TRIPS SCREENSHOT",
                  url: jobApplicant.driver.uber_trips_screenshot,
                },
              ].map((doc) => (
                <div key={doc.label} className="flex items-center">
                  <span className="text-orange-700 mr-2">
                    <FiLink size={12} />
                  </span>
                  <button
                    onClick={() => handleViewFile(doc.url, doc.label, 'application/pdf', `${jobApplicant.driver.first_name}_${jobApplicant.driver.last_name}`)}
                    className="text-orange-700 text-xs xl:text-lg font-semibold font-serif mr-2 hover:underline"
                  >
                    {doc.label}
                  </button>
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div className="md:w-[20%] flex flex-col gap-3 items-stretch md:mt-20 xl:mt-24 mb-5 md:mb-0 justify-end">
              <button
                className="w-full text-center px-7 py-1 bg-green-700 text-xs text-white rounded-lg hover:bg-green-800 transition duration-300"
                onClick={() => handleAcceptWithDialog(jobApplicant.id)}
                disabled={AcceptingApplicationId === jobApplicant.id}
              >
                {AcceptingApplicationId === jobApplicant.id
                  ? "Hiring..."
                  : "Hire Applicant"}
              </button>
              <button
                className="w-full text-center px-7 py-1 bg-orange-700 text-xs text-white rounded-lg hover:bg-orange-800 transition duration-300"
                onClick={() => handleRemoveWithDialog(jobApplicant.id)}
                disabled={removingApplicationId === jobApplicant.id}
              >
                {removingApplicationId === jobApplicant.id
                  ? "Rejecting..."
                  : "Reject Applicant"}
              </button>
              <button
                className="w-full text-center px-7 py-1 bg-blue-700 text-xs text-white rounded-lg hover:bg-blue-800 transition duration-300"
                onClick={() => handleViewRating(jobApplicant.driver.id)}
              >
                View Rating
              </button>
            </div>
          </div>
        ))
      ) : (
        <div className="w-full flex items-center justify-center h-[80%]">
          {loading ? (
            <p>Loading...</p>
          ) : (
            <p className="text-black text-lg xl:text-2xl font-serif font-bold">
              No job applicants.
            </p>
          )}
        </div>
      )}
      <FileViewerModal
        isOpen={isFileViewerOpen}
        onClose={() => setIsFileViewerOpen(false)}
        fileUrl={currentFileUrl}
        fileName={currentFileName}
        fileType={currentFileType}
        vehicleInfo={{
          partnerName: currentApplicantName,
        }}
      />
    </div>
  );
};

export default Applicants;
