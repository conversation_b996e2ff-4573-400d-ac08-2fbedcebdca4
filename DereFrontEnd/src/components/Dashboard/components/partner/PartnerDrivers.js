import React, { useState, useEffect } from "react";
import axios from "axios";
import { BASE_URL } from "../../../../services/config";
import FileViewerModal from "../common/FileViewerModal";

const PartnerDriver = ({ driverInfo }) => {
  const [isRemoving, setIsRemoving] = useState(false);
  const [showPopup, setShowPopup] = useState(false);
  const [isFileViewerOpen, setIsFileViewerOpen] = useState(false);
  const [currentFileUrl, setCurrentFileUrl] = useState('');
  const [currentFileName, setCurrentFileName] = useState('');
  const [currentFileType, setCurrentFileType] = useState('');
  const [isRatingModalOpen, setIsRatingModalOpen] = useState(false);
  const [driverRating, setDriverRating] = useState(null);
  const [isLoadingRating, setIsLoadingRating] = useState(false);

  useEffect(() => {
    if (showPopup) {
      const timer = setTimeout(() => {
        setShowPopup(false);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [showPopup]);

  const handleViewFile = (fileUrl, fileName, fileType) => {
    setCurrentFileUrl(fileUrl);
    setCurrentFileName(fileName);
    setCurrentFileType(fileType);
    setIsFileViewerOpen(true);
  };

  const handleRemoveDriver = async (driverId) => {
    setIsRemoving(true);
    try {
      await axios.post(`${BASE_URL}api/partner-drivers/${driverId}/unhire/`, {}, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
          Accept: "application/json",
        },
      });
      setShowPopup(true);
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("Error removing driver:", error);
    } finally {
      setIsRemoving(false);
    }
  };

  const handleViewRating = async () => {
    setIsLoadingRating(true);
    try {
      console.log("Fetching rating for driver ID:", driverInfo.id);
      console.log("Driver info:", driverInfo);
      
      const token = localStorage.getItem("token");
      if (!token) {
        throw new Error("Authentication token not found.");
      }
      
      const response = await axios.get(`${BASE_URL}api/drivers/${driverInfo.id}/rating/`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
      });
      
      console.log("Rating API Response:", response.data);
      console.log("Response type:", typeof response.data);
      console.log("Response keys:", Object.keys(response.data));
      
      setDriverRating(response.data);
      setIsRatingModalOpen(true);
    } catch (error) {
      console.error("Error fetching driver rating:", error);
      console.error("Error details:", {
        message: error.message,
        status: error.response?.status,
        data: error.response?.data,
        config: error.config
      });
      // Set driverRating to null to show the "no data" message
      setDriverRating(null);
      setIsRatingModalOpen(true);
    } finally {
      setIsLoadingRating(false);
    }
  };

  const getRatingColor = (rating) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 3.5) return "text-yellow-600";
    return "text-red-600";
  };

  const getRatingLevel = (rating) => {
    if (rating >= 4.5) return "Excellent";
    if (rating >= 3.5) return "Good";
    if (rating >= 2.5) return "Average";
    return "Poor";
  };

  return (
    <div className="bg-white bg-opacity-40 shadow-sm p-4 pb-8 mb-4 ms-7 me-2 mt-4 flex flex-col md:flex-row justify-start items-start xl:p-5 w-[95%] rounded-xl">
      {showPopup && (
        <div className="fixed top-0 font-serif left-1/2 transform -translate-x-1/2 bg-green-500 text-white p-4 rounded shadow-md z-50">
          Driver removed successfully
        </div>
      )}
      <div className="flex items-start gap-4 flex-col">
        <div className="flex items-center gap-2">
          <span className="text-sm">Driver ID: </span>
          <span className="text-orange-500 font-medium">{driverInfo.id}</span>
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-xs text-gray-500">• Active</span>
          {driverRating && (
            <span className={`text-xs px-2 py-1 rounded-full ${
              driverRating.current_rating > 0 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {driverRating.current_rating > 0 ? 'Rated' : 'No Rating'}
            </span>
          )}
        </div>
        {/* contacts & driver documents */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5 md:gap-32">
          {/* Contacts Section */}
          <div className="space-y-2">
            <h3 className="text-sm text-left underline font-bold uppercase text-black mb-3">
              Contacts
            </h3>
            <div className="space-y-3 text-left">
              <div className="flex flex-col">
                <span className="text-xs">
                  Name: {driverInfo.first_name} {driverInfo.last_name}
                </span>
              </div>
              <div className="flex flex-col">
                <span className="text-xs">Email: {driverInfo.email}</span>
              </div>
              <div className="flex flex-col">
                <span className="text-xs">
                  Phone: {driverInfo.mobile_number}
                </span>
              </div>
            </div>
          </div>

          {/* Documents Section */}
          <div className="space-y-2">
            <h3 className="text-sm text-left underline font-bold uppercase mb-3">
              Driver's Documents
            </h3>
            <div className="space-y-3 text-xs">
              <button
                onClick={() => handleViewFile(driverInfo.id_photo, "ID Photo", "image")}
                className="flex items-center gap-2 text-orange-700 hover:underline"
              >
                <div className="w-4 h-4 flex items-center justify-center">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <span>ID PHOTO</span>
              </button>
              <button
                onClick={() => handleViewFile(driverInfo.psv_photo, "PSV Photo", "image")}
                className="flex items-center gap-2 text-orange-700 hover:underline"
              >
                <div className="w-4 h-4 flex items-center justify-center">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <span>PSV PHOTO</span>
              </button>
              <button
                onClick={() => handleViewFile(driverInfo.license_photo, "Driver's License", "image")}
                className="flex items-center gap-2 text-orange-700 hover:underline"
              >
                <div className="w-4 h-4 flex items-center justify-center">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <span>DRIVER'S LICENSE</span>
              </button>
              <button
                onClick={() => handleViewFile(driverInfo.good_conduct_photo, "Good Conduct Certificate", "image")}
                className="flex items-center gap-2 text-orange-700 hover:underline"
              >
                <div className="w-4 h-4 flex items-center justify-center">
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <span>GOOD CONDUCT CERTIFICATE</span>
              </button>
            </div>
          </div>
          <div className="flex items-end gap-2 flex-col">
            <button
              className="px-4 py-1 bg-blue-600 text-xs text-white rounded-lg hover:bg-blue-700 transition duration-300 w-full"
              onClick={handleViewRating}
              disabled={isLoadingRating}
            >
              {isLoadingRating ? "Loading..." : "View Rating"}
            </button>
            <button
              className="px-7 py-1 bg-orange-700 text-xs text-white rounded-lg hover:bg-orange-800 transition duration-300 w-full"
              onClick={() => handleRemoveDriver(driverInfo.id)}
              disabled={isRemoving}
            >
              {isRemoving ? "Removing..." : "Remove Driver"}
            </button>
          </div>
        </div>
      </div>
      <FileViewerModal
        isOpen={isFileViewerOpen}
        onClose={() => setIsFileViewerOpen(false)}
        fileUrl={currentFileUrl}
        fileName={currentFileName}
        fileType={currentFileType}
        vehicleInfo={{
          partnerName: `${driverInfo.first_name}_${driverInfo.last_name}`,
        }}
      />

      {/* Rating Modal */}
      {isRatingModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-800">
                Driver Rating - {driverInfo.first_name} {driverInfo.last_name}
              </h2>
              <button
                onClick={() => setIsRatingModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {driverRating && driverRating.driver_id ? (
              <div className="space-y-4">
                {/* Debug info */}
                <div className="text-xs text-gray-500">
                  Debug: current_rating = {JSON.stringify(driverRating.current_rating)}, 
                  type = {typeof driverRating.current_rating},
                  driverRating keys = {Object.keys(driverRating).join(', ')}
                </div>
                
                {/* Status indicator */}
                <div className={`text-sm font-medium ${driverRating.current_rating > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  Status: {driverRating.current_rating > 0 ? 'Active' : 'Inactive'}
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded">
                    <div className="text-sm text-gray-600">Current Rating</div>
                    <div className={`text-2xl font-bold ${getRatingColor(driverRating.current_rating)}`}>
                      {driverRating.current_rating.toFixed(1)}
                    </div>
                    <div className="text-sm text-gray-500">{driverRating.rating_level || getRatingLevel(driverRating.current_rating)}</div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded">
                    <div className="text-sm text-gray-600">Total Points</div>
                    <div className="text-2xl font-bold text-blue-600">
                      {driverRating.total_points_all_time || 0}
                    </div>
                    <div className="text-sm text-gray-500">All Time</div>
                  </div>
                </div>

                {/* Payment Statistics */}
                {driverRating.payment_days > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Payment Statistics</h3>
                    <div className="grid grid-cols-3 gap-3">
                      <div className="bg-green-50 p-3 rounded">
                        <div className="text-sm text-gray-600">On-time</div>
                        <div className="text-lg font-bold text-green-600">{driverRating.on_time_payments}</div>
                      </div>
                      <div className="bg-yellow-50 p-3 rounded">
                        <div className="text-sm text-gray-600">Late</div>
                        <div className="text-lg font-bold text-yellow-600">{driverRating.late_payments}</div>
                      </div>
                      <div className="bg-red-50 p-3 rounded">
                        <div className="text-sm text-gray-600">Missed</div>
                        <div className="text-lg font-bold text-red-600">{driverRating.missed_payments}</div>
                      </div>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      Success Rate: {driverRating.success_rate}%
                    </div>
                  </div>
                )}

                {/* Recent Payment History */}
                {driverRating.recent_payments && driverRating.recent_payments.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Recent Payment History</h3>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {driverRating.recent_payments.slice(0, 5).map((payment, index) => (
                        <div key={index} className="bg-gray-50 p-2 rounded text-sm">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium">{payment.date}</div>
                              <div className="text-xs text-gray-600">{payment.vehicle}</div>
                            </div>
                            <div className="text-right">
                              <div className={`font-medium ${payment.status === 'on_time' ? 'text-green-600' : payment.status === 'late' ? 'text-yellow-600' : 'text-red-600'}`}>
                                {payment.amount}
                              </div>
                              <div className="text-xs text-gray-500 capitalize">{payment.status.replace('_', ' ')}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Vehicle History */}
                {driverRating.vehicle_history && driverRating.vehicle_history.length > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Vehicle History</h3>
                    <div className="space-y-2">
                      {driverRating.vehicle_history.map((vehicle, index) => (
                        <div key={index} className="bg-gray-50 p-3 rounded">
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium">{vehicle.registration}</div>
                              <div className="text-sm text-gray-600">{vehicle.make}</div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-gray-600">Points</div>
                              <div className="font-medium">{vehicle.total_points}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {driverRating.rating_calculation_info && (
                  <div>
                    <h3 className="font-semibold text-gray-800 mb-2">Rating Calculation</h3>
                    <div className="bg-gray-50 p-3 rounded">
                      <div className="text-sm text-gray-600">Method: {driverRating.rating_calculation_info.calculation_method}</div>
                      <div className="text-sm text-gray-600">Points on time: {driverRating.rating_calculation_info.points_on_time}</div>
                      <div className="text-sm text-gray-600">Points late: {driverRating.rating_calculation_info.points_late}</div>
                    </div>
                  </div>
                )}
              </div>
                          ) : (
                <div className="text-center text-gray-600">
                  <p>No rating data available. This driver has not recorded any payments yet.</p>
                  <p className="text-sm text-gray-500 mt-2">Status: Inactive</p>
                  <div className="mt-2 text-xs text-gray-500">
                    Debug: driverRating = {driverRating ? 'exists' : 'null'}, 
                    driver_id = {driverRating?.driver_id || 'none'},
                    keys = {driverRating ? Object.keys(driverRating).join(', ') : 'none'}
                  </div>
                </div>
              )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PartnerDriver;
