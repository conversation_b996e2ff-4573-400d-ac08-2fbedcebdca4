import React, { useEffect, useRef, useState } from "react";
import Chart from "chart.js/auto";
import axios from "axios";
import { BASE_URL } from "../../services/config";

const DriverRating = () => {
  const ratingChartRef = useRef(null);
  const paymentChartRef = useRef(null);
  const [driverData, setDriverData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [trendPeriod, setTrendPeriod] = useState('weekly'); // weekly or monthly
  const [paymentHistoryFilter, setPaymentHistoryFilter] = useState('all'); // all, on_time, late, missed
  const [dateRangeFilter, setDateRangeFilter] = useState('all'); // all, last_week, last_month, last_3_months
  const [filteredPaymentHistory, setFilteredPaymentHistory] = useState([]);

  useEffect(() => {
    const fetchDriverRating = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) {
          throw new Error("No authentication token found. Please log in again.");
        }

        // Get user data to get driver ID
        const userResponse = await axios.get(`${BASE_URL}api/users/me/`, {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        });

        const driverId = userResponse.data.driver_details.id;

        const response = await axios.get(
          `${BASE_URL}api/drivers/${driverId}/rating/`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              Accept: "application/json",
            },
          }
        );
        setDriverData(response.data);
        console.log("Driver Rating Data:", response.data);
      } catch (err) {
        console.error("Error fetching driver rating:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDriverRating();
  }, []);

  const fetchTrendData = async (period = 'weekly') => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        throw new Error("No authentication token found. Please log in again.");
      }

      const userResponse = await axios.get(`${BASE_URL}api/users/me/`, {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/json",
        },
      });

      const driverId = userResponse.data.driver_details.id;

      const response = await axios.get(
        `${BASE_URL}api/drivers/${driverId}/rating_trend/?period=${period}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            Accept: "application/json",
          },
        }
      );
      return response.data.trend_data || [];
    } catch (err) {
      console.error("Error fetching trend data:", err);
      return [];
    }
  };

  useEffect(() => {
    if (!driverData) return;

    const updateChart = async () => {
      const trendData = await fetchTrendData(trendPeriod);
      
      // Add a small delay to ensure DOM elements are fully rendered
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Chart 1: Rating Trend
      const ratingCtx = document.getElementById("ratingTrendChart");
      if (!ratingCtx) {
        console.warn("Rating trend chart element not found");
        return;
      }
      
      // Check if the canvas has valid dimensions
      if (ratingCtx.width === 0 || ratingCtx.height === 0) {
        console.warn("Rating trend chart canvas has no dimensions");
        return;
      }
      
      if (ratingChartRef.current) ratingChartRef.current.destroy();

      if (trendData && trendData.length > 0) {
        try {
          ratingChartRef.current = new Chart(ratingCtx, {
            type: "line",
            data: {
              labels: trendData.map((r) => r.week_label || r.month_label),
              datasets: [
                {
                  label: "Rating",
                  data: trendData.map((r) => r.rating),
                  borderColor: "#f97316",
                  backgroundColor: "rgba(249, 115, 22, 0.1)",
                  borderWidth: 3,
                  fill: true,
                  tension: 0.4,
                  pointBackgroundColor: "#ea580c",
                  pointBorderColor: "#fff",
                  pointBorderWidth: 2,
                  pointRadius: 6,
                },
              ],
            },
            options: {
              responsive: true,
              plugins: { legend: { display: false } },
              scales: { y: { beginAtZero: true, max: 5 } },
            },
          });
        } catch (error) {
          console.error("Error creating rating trend chart:", error);
        }
      } else {
        // Show a message when no trend data is available
        try {
          const ctx = ratingCtx.getContext('2d');
          ctx.clearRect(0, 0, ratingCtx.width, ratingCtx.height);
          ctx.font = '16px Arial';
          ctx.fillStyle = '#666';
          ctx.textAlign = 'center';
          ctx.fillText('No trend data available', ratingCtx.width / 2, ratingCtx.height / 2);
        } catch (error) {
          console.error("Error drawing no data message:", error);
        }
      }
    };

    updateChart();

    return () => {
      if (ratingChartRef.current) ratingChartRef.current.destroy();
      if (paymentChartRef.current) paymentChartRef.current.destroy();
    };
  }, [driverData, trendPeriod]);

  // Separate useEffect for payment chart that responds to filter changes
  useEffect(() => {
    if (!driverData) return;

    // Define payment history and filtered payment history here
    const paymentHistory = (driverData.recent_payment_history || []).map((p) => ({
      date: p.date,
      status: p.status.toLowerCase(),
    }));

    // Filter payment history based on selected filters
    const filtered = paymentHistory.filter((payment) => {
      // Filter by payment status
      if (paymentHistoryFilter !== 'all') {
        const statusMap = {
          'on_time': 'on time',
          'late': 'late',
          'missed': 'missed'
        };
        if (payment.status !== statusMap[paymentHistoryFilter]) {
          return false;
        }
      }

      // Filter by date range
      if (dateRangeFilter !== 'all') {
        const paymentDate = new Date(payment.date);
        const now = new Date();
        const daysDiff = Math.floor((now - paymentDate) / (1000 * 60 * 60 * 24));
        
        switch (dateRangeFilter) {
          case 'last_week':
            if (daysDiff > 7) return false;
            break;
          case 'last_month':
            if (daysDiff > 30) return false;
            break;
          case 'last_3_months':
            if (daysDiff > 90) return false;
            break;
          default:
            break;
        }
      }

      return true;
    });
    setFilteredPaymentHistory(filtered);

    const updatePaymentChart = async () => {
      // Add a small delay to ensure DOM elements are fully rendered
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Chart 2: Payment Performance
      const paymentCtx = document.getElementById("paymentChart");
      if (!paymentCtx) {
        console.warn("Payment chart element not found");
        return;
      }
      
      // Check if the canvas has valid dimensions
      if (paymentCtx.width === 0 || paymentCtx.height === 0) {
        console.warn("Payment chart canvas has no dimensions");
        return;
      }
      
      if (paymentChartRef.current) paymentChartRef.current.destroy();

      // Calculate filtered payment statistics
      const filteredStats = filtered.reduce((acc, payment) => {
        if (payment.status === 'on time') acc.onTime++;
        else if (payment.status === 'late') acc.late++;
        else if (payment.status === 'missed') acc.missed++;
        return acc;
      }, { onTime: 0, late: 0, missed: 0 });
      
      // Always show the chart, even if all values are 0
      const totalPayments = filteredStats.onTime + filteredStats.late + filteredStats.missed;
      const chartData = totalPayments > 0 ? [filteredStats.onTime, filteredStats.late, filteredStats.missed] : [1];
      const chartLabels = totalPayments > 0 ? ["On-time", "Late", "Missed"] : ["No Payments"];
      const chartColors = totalPayments > 0 ? ["#22c55e", "#eab308", "#ef4444"] : ["#e5e7eb"];

      try {
        paymentChartRef.current = new Chart(paymentCtx, {
          type: "doughnut",
          data: {
            labels: chartLabels,
            datasets: [
              {
                data: chartData,
                backgroundColor: chartColors,
                borderColor: "#fff",
                borderWidth: 3,
              },
            ],
          },
          options: {
            responsive: true,
            plugins: { 
              legend: { position: "bottom" },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    if (totalPayments === 0) {
                      return "No payments recorded";
                    }
                    const value = context.parsed;
                    const percentage = ((value / totalPayments) * 100).toFixed(1);
                    return `${context.label}: ${value} (${percentage}%)`;
                  }
                }
              }
            },
          },
        });
      } catch (error) {
        console.error("Error creating payment chart:", error);
      }
    };

    updatePaymentChart();

    return () => {
      if (paymentChartRef.current) paymentChartRef.current.destroy();
    };
  }, [driverData, paymentHistoryFilter, dateRangeFilter]);

  if (loading) {
    return (
      <div className="text-center mt-20">
        <div className="text-orange-600 font-bold">Loading rating data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center mt-20">
        <div className="text-red-600 font-bold">
          Error loading rating data: {error}
        </div>
      </div>
    );
  }

  if (!driverData) {
    return (
      <div className="text-center mt-20">
        <div className="text-orange-600 font-bold">No rating data available</div>
      </div>
    );
  }

  // Check if driver has any rating data
  const hasRatingData = driverData.current_rating !== null && 
                       driverData.current_rating !== undefined && 
                       driverData.current_rating > 0;

  if (!hasRatingData) {
    return (
      <div className="text-center mt-20">
        <div className="text-orange-600 font-bold mb-4">No Rating Data Available</div>
        <div className="text-gray-600 max-w-md mx-auto">
          <p className="mb-4">You don't have any rating data yet. Ratings are calculated based on your payment performance.</p>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h3 className="font-semibold text-orange-800 mb-2">How to get rated:</h3>
            <ul className="text-sm text-orange-700 space-y-1">
              <li>• Make payments on time to earn 5 points</li>
              <li>• Late payments earn 2.5 points</li>
              <li>• Missed payments earn 0 points</li>
              <li>• Your rating improves with consistent payments</li>
            </ul>
          </div>
        </div>
      </div>
    );
  }



  const totalPoints =
    (driverData.on_time_payments || 0) * 5 + (driverData.late_payments || 0) * 2.5;

  const currentRating =
    (driverData.payment_days || 0) > 0
      ? (totalPoints / (driverData.payment_days || 1)).toFixed(1)
      : (0).toFixed(1);

  const summaryStats = [
    ["📅", driverData.payment_days || 0, "Payment Days"],
    ["✅", driverData.on_time_payments || 0, "On-time"],
    ["⏰", driverData.late_payments || 0, "Late Payments"],
    ["❌", driverData.missed_payments || 0, "Missed Payments"],
    ["🏆", totalPoints, "Total Points "],
    ["📊", `${driverData.success_rate || 0}%`, "Success Rate"],
  ];
  console.log("Summary Stats:", summaryStats);

  return (
    <div className="h-screen font-serif overflow-y-scroll bg-gray-100 font-serif" style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}>
      <style>{`div::-webkit-scrollbar { display: none; }`}</style>

      <div className="w-full px-4 md:px-8 lg:px-16 py-4 space-y-10 text-gray-800">
        {/* Header */}
        <div className="bg-gradient-to-br from-orange-600 to-orange-400 text-white p-6 rounded-3xl shadow-xl relative overflow-hidden mt-10">
          <div className="flex flex-col md:flex-row items-center gap-6 relative z-10">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center font-bold text-2xl border-4 border-white/30">
              {driverData.driver_name.split(" ").map((n) => n[0]).join("")}
            </div>
            <div className="flex-1">
              <h1 className="text-3xl font-bold">{driverData.driver_name}</h1>
              <p>Vehicle: {driverData.current_vehicle}</p>
            </div>
            <div className="text-center bg-white/10 p-4 rounded-xl backdrop-blur">
              <div className="text-4xl font-extrabold text-white drop-shadow">
                {driverData.current_rating ? driverData.current_rating.toFixed(1) : currentRating}
              </div>
              <div className="text-sm font-semibold">⭐ Current Rating</div>
            </div>
          </div>
        </div>

        {/* Performance Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          {summaryStats.map(([icon, value, label]) => (
            <div key={label} className="bg-white p-4 rounded-xl shadow hover:-translate-y-1 transition-all">
              <div className="w-12 h-12 bg-orange-100 text-orange-600 mx-auto rounded-full flex items-center justify-center text-xl mb-2">{icon}</div>
              <div className="text-2xl font-bold text-orange-600">{value}</div>
              <div className="text-sm font-medium text-gray-600">{label}</div>
            </div>
          ))}
        </div>

        {/* Filter Controls */}
        <div className="bg-white p-6 rounded-xl shadow">
          <h3 className="text-lg font-semibold text-orange-600 mb-4">📊 Filter Options</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Rating Trend Period Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Rating Trend Period</label>
              <select
                value={trendPeriod}
                onChange={(e) => setTrendPeriod(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                  trendPeriod !== 'weekly' ? 'border-orange-300 bg-orange-50' : 'border-gray-300'
                }`}
              >
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>

            {/* Payment History Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
              <select
                value={paymentHistoryFilter}
                onChange={(e) => setPaymentHistoryFilter(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                  paymentHistoryFilter !== 'all' ? 'border-orange-300 bg-orange-50' : 'border-gray-300'
                }`}
              >
                <option value="all">All Payments</option>
                <option value="on_time">On-time Only</option>
                <option value="late">Late Only</option>
                <option value="missed">Missed Only</option>
              </select>
            </div>

            {/* Date Range Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <select
                value={dateRangeFilter}
                onChange={(e) => setDateRangeFilter(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                  dateRangeFilter !== 'all' ? 'border-orange-300 bg-orange-50' : 'border-gray-300'
                }`}
              >
                <option value="all">All Time</option>
                <option value="last_week">Last Week</option>
                <option value="last_month">Last Month</option>
                <option value="last_3_months">Last 3 Months</option>
              </select>
            </div>
          </div>
          
          {/* Filter Summary */}
          {(paymentHistoryFilter !== 'all' || dateRangeFilter !== 'all' || trendPeriod !== 'weekly') && (
            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <p className="text-sm text-orange-700">
                <strong>Active Filters:</strong> 
                {trendPeriod !== 'weekly' && ` ${trendPeriod} trend,`}
                {paymentHistoryFilter !== 'all' && ` ${paymentHistoryFilter.replace('_', ' ')} payments,`}
                {dateRangeFilter !== 'all' && ` ${dateRangeFilter.replace('_', ' ')}`}
                {` (${filteredPaymentHistory.length} payments shown)`}
              </p>
            </div>
          )}
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-xl shadow">
            <h2 className="text-center font-semibold text-orange-600 mb-4">
              📈 Rating Trend ({trendPeriod === 'weekly' ? 'Weekly' : 'Monthly'})
            </h2>
            <canvas id="ratingTrendChart"></canvas>
          </div>
          <div className="bg-white p-6 rounded-xl shadow">
            <h2 className="text-center font-semibold text-orange-600 mb-4">💳 Payment Performance %</h2>
            <canvas id="paymentChart"></canvas>
          </div>
        </div>

        {/* Payment History */}
        <div className="bg-white rounded-xl shadow overflow-hidden">
          <h2 className="bg-orange-100 text-orange-600 text-center font-semibold py-3">
            📆 Recent Payment History ({filteredPaymentHistory.length} payments)
          </h2>
          <div className="max-h-80 overflow-y-auto divide-y divide-orange-100">
            {filteredPaymentHistory.length > 0 ? (
              filteredPaymentHistory.map((p, i) => (
                <div key={i} className="flex justify-between items-center p-4 hover:bg-orange-50">
                  <div className="font-semibold text-orange-600">
                    {new Date(p.date).toLocaleDateString("en-US", {
                      weekday: "short",
                      month: "short",
                      day: "numeric",
                    })}
                  </div>
                  <div className={`text-sm px-3 py-1 rounded-full font-medium ${
                    p.status === "on time"
                      ? "bg-green-500 text-white"
                      : p.status === "late"
                      ? "bg-yellow-500 text-white"
                      : "bg-red-500 text-white"
                  }`}>
                    {p.status === "on time" ? "On Time" : p.status === "late" ? "Late" : "Missed"}
                  </div>
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500">
                No payments found with the selected filters.
              </div>
            )}
          </div>
        </div>

        {/* Tips Section */}
        <div className="bg-white rounded-xl p-6 shadow mb-10">
          <h2 className="text-center text-xl font-semibold text-orange-600 mb-4">💡 Tips to Improve Your Rating</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            {[
              ["⏰", "Make payments before the deadline to earn full 5 points"],
              ["📱", "Set up payment reminders on your phone"],
              ["💰", "Late (2.5 pts) is better than missed (0 pts)"],
              ["📈", "Consistent payments boost your rating"],
            ].map(([icon, tip], i) => (
              <div key={i} className="bg-orange-100 p-4 rounded-lg text-center text-orange-700">
                <div className="text-2xl mb-2">{icon}</div>
                <div className="font-medium">{tip}</div>
              </div>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="bg-white rounded-xl p-6 shadow mb-10">
          <h2 className="text-center text-xl font-semibold text-orange-600 mb-4">
            ❓ How Your Rating is Calculated (FAQ)
          </h2>
          <div className="space-y-4 text-left text-sm md:text-base">
            <div>
              <h3 className="font-bold text-gray-800">What is my rating based on?</h3>
              <p className="text-gray-600 mt-1">
                Your rating is primarily based on your payment performance. It's a score out of 5 that reflects how consistently you make your payments.
              </p>
            </div>
            <div>
              <h3 className="font-bold text-gray-800">How are points awarded for payments?</h3>
              <p className="text-gray-600 mt-1">
                Points are awarded for each payment cycle based on when you pay:
              </p>
              <ul className="list-disc list-inside text-gray-600 mt-1 pl-4">
                <li><span className="font-semibold">On-time payment:</span> 5 points</li>
                <li><span className="font-semibold">Late payment:</span> 2.5 points</li>
                <li><span className="font-semibold">Missed payment:</span> 0 points</li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold text-gray-800">How is the overall rating calculated?</h3>
              <p className="text-gray-600 mt-1">
                Your overall rating is an average of the points you've earned from your recent payments. The "Rating Trend" chart shows how your rating has changed over time.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DriverRating;
