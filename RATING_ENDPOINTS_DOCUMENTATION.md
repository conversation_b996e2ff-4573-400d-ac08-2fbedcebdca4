# Driver Rating System API Documentation

## Overview

The Driver Rating System is a comprehensive solution that calculates and tracks driver performance based on payment behavior. The system evaluates drivers on their payment timeliness, consistency, and adherence to payment schedules.

## Rating Calculation Logic

### Core Rating Formula
```
Rating Score = Total Points / Total Payment Days
```

### Point System
- **On Time Payment**: 5.0 points
- **Late Payment**: 2.5 points  
- **Missed Payment**: 0.0 points

### Rating Levels
- **Excellent**: 4.5 - 5.0
- **Good**: 3.5 - 4.4
- **Average**: 2.5 - 3.4
- **Poor**: 1.0 - 2.4
- **Very Poor**: 0.0 - 0.9

### Payment Status Determination
The system automatically calculates payment status based on:

1. **Payment Settings**: Vehicle-specific or partner-wide payment settings
2. **Payment Day**: Whether the payment date is a configured payment day
3. **Payment Time**: Whether payment was made before or after the deadline time
4. **Automatic Status Calculation**: Status is calculated and stored when Revenue records are created/updated
5. **Status Updates on Edit**: When a payment is edited, status is recalculated based on new creation time

**Status Logic**:
- **On Time**: Payment made on a payment day before or at the deadline time
- **Late**: Payment made on a payment day after the deadline time
- **Missed**: No payment made on an expected payment day (creates automatic missed revenue record)

**Status Update Scenarios**:
1. **New Payment for Past Date**: Updates any existing missed revenue records for that date to 'late'
2. **Payment Edit**: Recalculates status based on new creation time vs deadline
3. **Payment Deletion**: Maintains missed revenue records for expected payment days

### Automatic Missed Revenue Creation
The system automatically creates missed revenue records for expected payment days when no payment is made:

1. **Expected Payment Days**: Calculated based on payment settings (e.g., Monday, Wednesday, Friday)
2. **Missing Payment Detection**: When no revenue record exists for an expected payment day
3. **Automatic Creation**: Creates revenue record with status 'missed' and amount from payment settings
4. **Late Payment Update**: When a payment is made for a past date, updates existing missed records to 'late'
5. **Edit Handling**: When a payment is edited, status is recalculated and any missed records are updated accordingly

### Inactive Rating Conditions
- Rating becomes inactive after 3+ consecutive non-payment days
- Rating reactivates when a recent payment is made (within 3 days)

## API Endpoints

### 1. Get Driver Rating
**Endpoint**: `GET /api/drivers/{driver_id}/rating/`

**Description**: Retrieves comprehensive rating dashboard for a specific driver

**Permissions**:
- Drivers can only view their own rating
- Partners can view ratings for their own drivers, job applicants, or any driver (testing mode)

**Response**:
```json
{
  "driver_id": 123,
  "driver_name": "John Doe",
  "driver_code": "DRV-0123",
  "age_group": "26-39 years",
  "current_rating": 4.2,
  "rating_level": "Good",
  "total_points": 125.5,
  "total_payment_days": 30,
  "payment_performance": {
    "on_time_payments": 25,
    "late_payments": 3,
    "missed_payments": 2,
    "success_rate": 83.33
  },
  "recent_payment_history": [
    {
      "date": "2025-01-15",
      "amount": 1000.00,
      "status": "on_time",
      "vehicle_registration": "KCA 123A"
    }
  ],
  "vehicle_ratings": [
    {
      "vehicle_id": 456,
      "vehicle_registration": "KCA 123A",
      "rating_score": 4.2,
      "total_points": 125.5,
      "payment_days": 30,
      "is_active": true
    }
  ]
}
```

### 2. Calculate Driver Rating
**Endpoint**: `POST /api/drivers/{driver_id}/calculate_rating/`

**Description**: Manually triggers rating calculation for a specific driver

**Permissions**: Partners and Admins only

**Request Body**:
```json
{
  "recalculate_from_date": "2025-01-01",
  "vehicle_id": 456
}
```

**Parameters**:
- `recalculate_from_date` (optional): Start date for recalculation (YYYY-MM-DD)
- `vehicle_id` (optional): Calculate for specific vehicle only

**Response**:
```json
{
  "driver_id": 123,
  "driver_name": "John Doe",
  "calculation_triggered": true,
  "calculation_timestamp": "2025-01-15T10:30:00Z",
  "recalculated_from_date": "2025-01-01",
  "overall_rating": 4.2,
  "total_points_all_time": 1255,
  "vehicles_processed": 2,
  "vehicle_results": [
    {
      "vehicle_id": 456,
      "vehicle_registration": "KCA 123A",
      "rating_updated": true,
      "current_rating": 4.2,
      "total_points": 125.5,
      "payment_days": 30
    }
  ]
}
```

### 3. Get Driver Rating Trend
**Endpoint**: `GET /api/drivers/{driver_id}/rating_trend/`

**Description**: Retrieves rating trend data for a specific driver

**Parameters**:
- `period` (optional): "weekly" or "monthly" (default: "weekly")
- `weeks` (optional): Number of weeks for weekly trend (default: 8)
- `months` (optional): Number of months for monthly trend (default: 6)

**Response**:
```json
{
  "driver_id": 123,
  "driver_name": "John Doe",
  "period": "weekly",
  "trend_data": [
    {
      "week_number": 1,
      "week_label": "Week 1",
      "start_date": "2025-01-06",
      "end_date": "2025-01-12",
      "rating": 4.2,
      "payments_made": 3
    }
  ],
  "trend_direction": "improving",
  "average_rating": 4.1
}
```

### 4. Get All Drivers Rating Trends
**Endpoint**: `GET /api/drivers/rating_trends/`

**Description**: Retrieves rating trends for all partner drivers with filtering

**Parameters**:
- `driver_name` (optional): Filter by driver name
- `vehicle` (optional): Filter by vehicle registration
- `min_age` (optional): Minimum driver age
- `max_age` (optional): Maximum driver age
- `start_date` (optional): Start date filter (YYYY-MM-DD)
- `end_date` (optional): End date filter (YYYY-MM-DD)
- `period` (optional): "weekly" or "monthly" (default: "weekly")

**Response**:
```json
{
  "partner_id": 1,
  "partner_name": "ABC Transport",
  "trends_data": {
    "John Doe": [
      {
        "week_number": 1,
        "week_label": "Week 1",
        "start_date": "2025-01-06",
        "end_date": "2025-01-12",
        "rating": 4.2,
        "payments_made": 3
      }
    ]
  },
  "period": "weekly",
  "filters_applied": {
    "driver_name": "",
    "vehicle": "",
    "min_age": "20",
    "max_age": "30",
    "start_date": "",
    "end_date": "",
    "period": "weekly"
  },
  "total_drivers": 3
}
```

### 5. Performance Report
**Endpoint**: `GET /api/drivers/performance_report/`

**Description**: Retrieves paginated driver performance report with advanced filtering

**Parameters**:
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 20, max: 100)
- `driver_name` (optional): Filter by driver name
- `vehicle` (optional): Filter by vehicle registration
- `min_age` (optional): Minimum driver age
- `max_age` (optional): Maximum driver age
- `start_date` (optional): Start date filter (YYYY-MM-DD)
- `end_date` (optional): End date filter (YYYY-MM-DD)
- `status` (optional): "all", "active", "inactive" (default: "all")
- `sort_by` (optional): "rating", "name", "success_rate", "total_points" (default: "rating")
- `sort_order` (optional): "asc", "desc" (default: "desc")

**Response**:
```json
{
  "partner_id": 1,
  "partner_name": "ABC Transport",
  "summary": {
    "total_drivers": 5,
    "active_drivers": 4,
    "inactive_drivers": 1,
    "average_rating": 3.8,
    "total_points_all_drivers": 1255
  },
  "pagination": {
    "current_page": 1,
    "page_size": 20,
    "total_pages": 1,
    "total_records": 5,
    "has_next": false,
    "has_previous": false,
    "next_page": null,
    "previous_page": null
  },
  "filters_applied": {
    "driver_name": "",
    "vehicle": "",
    "min_age": "20",
    "max_age": "30",
    "start_date": "",
    "end_date": "",
    "status": "all",
    "sort_by": "rating",
    "sort_order": "desc"
  },
  "drivers": [
    {
      "driver_id": 123,
      "driver_name": "John Doe",
      "driver_code": "DRV-0123",
      "vehicle": "KCA 123A",
      "age_group": "26-39 years",
      "current_rating": 4.2,
      "rating_level": "Good",
      "payment_days": 30,
      "on_time_payments": 25,
      "late_payments": 3,
      "missed_payments": 2,
      "total_points_period": 125.5,
      "total_points_all_time": 1255,
      "success_rate": 83.33,
      "is_active": true,
      "status": "Active"
    }
  ]
}
```

### 6. Get Driver Payment History
**Endpoint**: `GET /api/drivers/{driver_id}/payment_history/`

**Description**: Retrieves paginated payment history for a specific driver with advanced filtering

**Permissions**:
- Drivers can only view their own payment history
- Partners can view payment history for their own drivers

**Parameters**:
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 20, max: 100)
- `start_date` (optional): Start date filter (YYYY-MM-DD)
- `end_date` (optional): End date filter (YYYY-MM-DD)
- `days` (optional): Number of days from today (alternative to start_date/end_date)
- `vehicle_id` (optional): Filter by specific vehicle ID
- `vehicle_registration` (optional): Filter by vehicle registration number
- `status` (optional): "all", "on_time", "late" (default: "all")
- `min_amount` (optional): Minimum payment amount
- `max_amount` (optional): Maximum payment amount

**Response**:
```json
{
  "driver_id": 123,
  "driver_name": "John Doe",
  "driver_code": "DRV-0123",
  "total_points_all_time": 1255,
  "pagination": {
    "current_page": 1,
    "page_size": 20,
    "total_pages": 3,
    "total_records": 45,
    "has_next": true,
    "has_previous": false,
    "next_page": 2,
    "previous_page": null
  },
  "filters_applied": {
    "start_date": "2025-01-01",
    "end_date": "2025-01-31",
    "status": "all",
    "vehicle_id": "",
    "min_amount": "",
    "max_amount": ""
  },
  "summary": {
    "total_payments": 45,
    "on_time_payments": 38,
    "late_payments": 5,
    "missed_payments": 2,
    "total_amount": 45000.00,
    "average_amount": 1000.00,
    "success_rate": 84.44
  },
  "payment_history": [
    {
      "id": 789,
      "date": "2025-01-15",
      "formatted_date": "Wed, Jan 15, 2025",
      "amount": 1000.00,
      "vehicle": {
        "id": 456,
        "registration": "KCA 123A",
        "make": "Toyota",
        "model": "Hilux"
      },
      "status": "On Time",
      "status_code": "on_time",
      "points": 5.0,
      "created_at": "2025-01-15T10:30:00Z"
    }
  ]
}
```

### 7. Daily Payment Report
**Endpoint**: `GET /api/daily-payment-report/`

**Description**: Retrieves daily payment status for all partner vehicles

**Permissions**: Partners only

**Parameters**:
- `date` (optional): Report date (YYYY-MM-DD, defaults to today)
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 10)

**Response**:
```json
{
  "count": 15,
  "next": "?page=2",
  "previous": null,
  "total_pages": 2,
  "current_page": 1,
  "page_size": 10,
  "results": [
    {
      "vehicle": {
        "id": 456,
        "registration": "KCA 123A",
        "make": "Toyota",
        "model": "Hilux",
        "partner": "ABC Transport"
      },
      "driver": {
        "id": 123,
        "name": "John Doe",
        "code": "DRV-0123"
      },
      "amount_paid": 1000.00,
      "confirmation_message": "Payment confirmed",
      "status": "Paid",
      "driver_rating": {
        "current_rating": 4.2,
        "total_points": 125.5,
        "payment_days": 30,
        "is_active": true,
        "consecutive_non_payment_days": 0
      }
    }
  ]
}
```

### 8. Recalculate All Driver Ratings
**Endpoint**: `POST /api/partners/recalculate_all_driver_ratings/`

**Description**: Recalculates ratings for all drivers of a partner

**Permissions**: Partners only

**Request Body**:
```json
{
  "recalculate_from_date": "2025-01-01"
}
```

**Response**:
```json
{
  "partner_id": 1,
  "partner_name": "ABC Transport",
  "calculation_triggered": true,
  "calculation_timestamp": "2025-01-15T10:30:00Z",
  "drivers_processed": 5,
  "ratings_updated": 5,
  "errors": 0,
  "summary": {
    "total_drivers": 5,
    "active_drivers": 4,
    "inactive_drivers": 1,
    "average_rating": 3.8
  }
}
```

## Vehicle Assignment and Partner Transfer

### Vehicle Assignment
**Endpoint**: `PATCH /api/vehicles/{vehicle_id}/assign_driver/`

**Description**: Assigns or removes a driver from a vehicle

**Permissions**: Vehicle owner (partner) only

**Request Body**:
```json
{
  "driver_id": 123
}
```

**Response**:
```json
{
  "success": "Driver 123 assigned to vehicle 456."
}
```

**Vehicle Assignment Rules**:
1. Only one driver can be assigned to a vehicle at a time
2. A driver cannot be assigned to multiple vehicles simultaneously
3. When a driver is assigned to a new vehicle, their previous vehicle assignment is automatically removed
4. Vehicle assignment triggers rating calculation for the new driver-vehicle combination

### Vehicle Removal
**Endpoint**: `PATCH /api/vehicles/{vehicle_id}/remove_vehicle/`

**Description**: Removes a vehicle (sets status to inactive) and clears driver association

**Response**:
```json
{
  "success": "Vehicle KCA 123A has been removed and driver association cleared."
}
```

### Partner Transfer Handling

When a driver transfers between partners:

1. **Rating History Preservation**: All historical rating data is preserved
2. **New Rating Creation**: New `DriverRating` records are created for new driver-vehicle combinations
3. **Partner Association**: Ratings are associated with the vehicle's partner
4. **Calculation Continuity**: Rating calculations continue from the earliest revenue record

## Payment Settings Integration

### Payment Settings Model
```python
class PaymentSettings(models.Model):
    partner = models.ForeignKey('Partner')
    vehicle = models.ForeignKey('Vehicle', null=True, blank=True)
    daily_amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_days = models.CharField(max_length=100)  # "Monday,Wednesday,Friday"
    deadline_time = models.TimeField()  # 17:00:00 for 5:00 PM
```

### Expected Payment Days Calculation
The system calculates expected payment days based on:
1. Vehicle-specific payment settings (if available)
2. Partner-wide payment settings (fallback)
3. Payment days configuration (e.g., "Monday,Wednesday,Friday")
4. Date range from calculation start to current date

### Payment Deadline Calculation
```python
def get_payment_deadline(vehicle, payment_date):
    # Get payment settings (vehicle-specific or partner-wide)
    # Check if payment_date is a configured payment day
    # Combine payment_date with deadline_time
    # Return deadline datetime
```

## Management Commands

### 1. Recalculate Payment Status
**Command**: `python manage.py recalculate_payment_status`

**Description**: Recalculates payment status for all revenue records based on payment date vs creation date and payment settings

**Usage**:
```bash
# Recalculate all payment statuses
python manage.py recalculate_payment_status
```

**What it does**:
- Compares payment creation time with deadline time
- Updates status to 'late' if payment was created after deadline
- Updates status to 'on_time' if payment was created before deadline
- Marks as 'missed' if not a payment day

### 2. Calculate Existing Ratings
**Command**: `python manage.py calculate_existing_ratings`

**Description**: Calculates ratings for existing drivers based on their payment history

**Usage**:
```bash
# Calculate ratings for all drivers (last 30 days)
python manage.py calculate_existing_ratings

# Calculate ratings for specific date range
python manage.py calculate_existing_ratings --start-date 2025-01-01 --end-date 2025-01-31

# Calculate rating for specific driver
python manage.py calculate_existing_ratings --driver-id 123

# Calculate rating for specific vehicle
python manage.py calculate_existing_ratings --vehicle-id 456

# Calculate ratings for specific partner
python manage.py calculate_existing_ratings --partner-id 1

# Dry run (show what would be calculated without creating)
python manage.py calculate_existing_ratings --dry-run

# Force recalculation even if ratings exist
python manage.py calculate_existing_ratings --force
```

**Parameters**:
- `--start-date`: Start date for calculation (YYYY-MM-DD)
- `--end-date`: End date for calculation (YYYY-MM-DD)
- `--driver-id`: Calculate for specific driver only
- `--vehicle-id`: Calculate for specific vehicle only
- `--partner-id`: Calculate for all drivers of specific partner
- `--dry-run`: Show what would be calculated without creating
- `--force`: Recalculate even if ratings already exist

### 3. Create Missed Revenue Records
**Command**: `python manage.py create_missed_revenue_records`

**Description**: Creates missed revenue records for expected payment days when no payment was made

**Usage**:
```bash
# Create missed records for all partners (last 30 days)
python manage.py create_missed_revenue_records

# Create missed records for specific date range
python manage.py create_missed_revenue_records --start-date 2025-01-01 --end-date 2025-01-31

# Create missed records for specific partner
python manage.py create_missed_revenue_records --partner-id 1

# Dry run (show what would be created without creating)
python manage.py create_missed_revenue_records --dry-run
```

**Parameters**:
- `--start-date`: Start date for checking (YYYY-MM-DD)
- `--end-date`: End date for checking (YYYY-MM-DD)
- `--partner-id`: Create missed records for specific partner only
- `--dry-run`: Show what would be created without actually creating records

**What it does**:
- Checks all vehicles for each partner
- Identifies expected payment days based on payment settings
- Creates missed revenue records for days with no payment
- Sets status to 'missed' and amount from payment settings
- Updates existing missed records to 'late' when payments are made

### 4. Update Payment Statuses
**Command**: `python manage.py update_payment_statuses`

**Description**: Updates payment statuses for existing revenue records based on payment settings and creation time

**Usage**:
```bash
# Update statuses for all revenue records (last 30 days)
python manage.py update_payment_statuses

# Update statuses for specific date range
python manage.py update_payment_statuses --start-date 2025-01-01 --end-date 2025-01-31

# Update statuses for specific partner
python manage.py update_payment_statuses --partner-id 1

# Update statuses for specific vehicle
python manage.py update_payment_statuses --vehicle-id 456

# Dry run (show what would be updated without updating)
python manage.py update_payment_statuses --dry-run
```

**Parameters**:
- `--start-date`: Start date for checking (YYYY-MM-DD)
- `--end-date`: End date for checking (YYYY-MM-DD)
- `--partner-id`: Update statuses for specific partner only
- `--vehicle-id`: Update statuses for specific vehicle only
- `--dry-run`: Show what would be updated without actually updating

**What it does**:
- Recalculates payment status for existing revenue records
- Compares creation time with deadline from payment settings
- Updates status to 'on_time', 'late', or 'missed' as appropriate
- Provides detailed logging of status changes
- Supports filtering by date range, partner, or vehicle

### 11. Delete Duplicates
**Command**: `python manage.py delete_duplicates`

**Description**: Removes duplicate records from the database

**Usage**:
```bash
python manage.py delete_duplicates
```

## Rating Calculation Process

### Step-by-Step Process

1. **Create Missed Revenue Records**
   - Check for expected payment days based on payment settings
   - Create missed revenue records for days with no payment
   - Set status to 'missed' and amount from payment settings

2. **Get Expected Payment Days**
   - Retrieve payment settings for vehicle/partner
   - Calculate all expected payment dates in the period

3. **Get Actual Payments**
   - Query revenue records for driver-vehicle combination
   - Map payments to expected payment dates

4. **Calculate Points for Each Day**
   - **Payment Made**: Use stored status from Revenue model (on_time/late/missed)
   - **No Payment**: Use missed revenue record with 0 points
   - **Status-based Points**: 5.0 for on_time, 2.5 for late, 0.0 for missed

5. **Update Rating Record**
   - Calculate total points and payment days
   - Update rating score: `total_points / total_payment_days`
   - Update consecutive non-payment days
   - Set `is_active` to False if 3+ consecutive non-payment days

6. **Handle Rating Reactivation**
   - Check for recent payments (within 3 days)
   - Reactivate rating if recent payment found

### Rating Calculation Example

**Scenario**: Driver has payment settings for Monday, Wednesday, Friday
- **Week 1**: 3 expected payments
- **Actual Payments**: 2 on-time, 1 late
- **Missed Payments**: 0 (system creates missed revenue records automatically)
- **Points**: (2 × 5.0) + (1 × 2.5) = 12.5 points
- **Rating**: 12.5 / 3 = 4.17

### Automatic Status Calculation

When a Revenue record is created or updated:

1. **Check Payment Settings**: Get vehicle-specific or partner-wide settings
2. **Verify Payment Day**: Check if payment date is a configured payment day
3. **Calculate Deadline**: Combine payment date with deadline time from settings
4. **Compare Times**: Compare creation time with deadline
5. **Set Status**: 
   - `on_time` if created before/at deadline
   - `late` if created after deadline
   - `missed` if not a payment day

## Error Handling

### Common Error Responses

**403 Forbidden**:
```json
{
  "detail": "You can only view your own rating."
}
```

**404 Not Found**:
```json
{
  "detail": "Driver not found."
}
```

**500 Internal Server Error**:
```json
{
  "driver_id": 123,
  "calculation_triggered": false,
  "error": "Error message"
}
```

## Best Practices

### For Partners
1. Set up payment settings for each vehicle
2. Regularly monitor driver ratings
3. Use the performance report for comprehensive analysis
4. Recalculate ratings after significant changes
5. Use daily payment reports to track daily payment status
6. Run management commands regularly for data consistency

### For Drivers
1. Make payments on time to maintain high ratings
2. Monitor rating trends to track performance
3. Contact partner for any payment issues

### For Developers
1. Always check permissions before accessing rating data
2. Handle date formats consistently (YYYY-MM-DD)
3. Use appropriate error handling for rating calculations
4. Consider performance implications for large datasets
5. Use management commands for bulk operations
6. Test rating calculations with dry-run mode

## Rate Limiting

- **GET requests**: 100 requests per minute per user
- **POST requests**: 20 requests per minute per user
- **Rating calculations**: 10 requests per minute per partner

## Authentication

All endpoints require Bearer token authentication:
```
Authorization: Bearer <token>
```

## Versioning

Current API version: v1
Base URL: `/api/v1/`

## Support

For technical support or questions about the rating system, contact the development team. 