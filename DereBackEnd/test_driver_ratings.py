#!/usr/bin/env python
"""
Test script for driver rating system implementation.
Run this script to test the driver rating functionality.
"""

import os
import sys
import django
from datetime import datetime, timedelta

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'akukoproject.settings')
django.setup()

from akuko_api.models import Partner, Driver, Vehicle, Revenue, PaymentSettings, DriverRating
from akuko_api.rating_utils import (
    calculate_driver_rating,
    create_missed_revenue_records,
    get_driver_age_group,
    create_or_update_driver_rating
)
from django.utils import timezone


def test_driver_rating_system():
    """Test the driver rating system functionality"""
    print("🚀 Testing Driver Rating System")
    print("=" * 50)
    
    # Test 1: Check if we have any partners
    partners = Partner.objects.all()
    print(f"📊 Found {partners.count()} partners")
    
    if not partners.exists():
        print("❌ No partners found. Please create some partners first.")
        return
    
    partner = partners.first()
    print(f"👤 Testing with partner: {partner.first_name} {partner.last_name}")
    
    # Test 2: Check if partner has vehicles with drivers
    vehicles_with_drivers = Vehicle.objects.filter(
        partner=partner,
        driver__isnull=False,
        status='active'
    )
    print(f"🚗 Found {vehicles_with_drivers.count()} vehicles with drivers")
    
    if not vehicles_with_drivers.exists():
        print("❌ No vehicles with drivers found. Please assign drivers to vehicles first.")
        return
    
    vehicle = vehicles_with_drivers.first()
    driver = vehicle.driver
    print(f"👨‍💼 Testing with driver: {driver.first_name} {driver.last_name}")
    print(f"🚙 Vehicle: {vehicle.registration_number}")
    
    # Test 3: Check payment settings
    payment_settings = PaymentSettings.objects.filter(
        vehicle=vehicle
    ).first() or PaymentSettings.objects.filter(
        partner=partner,
        vehicle__isnull=True
    ).first()
    
    if not payment_settings:
        print("❌ No payment settings found. Please create payment settings first.")
        return
    
    print(f"💰 Payment settings found: {payment_settings.daily_amount} per day")
    print(f"📅 Payment days: {payment_settings.payment_days}")
    
    # Test 4: Test driver age group function
    age_group = get_driver_age_group(driver)
    print(f"🎂 Driver age group: {age_group}")
    
    # Test 5: Test create_or_update_driver_rating function
    print("\n🔄 Testing create_or_update_driver_rating function...")
    try:
        rating = create_or_update_driver_rating(driver, vehicle, partner)
        print(f"✅ Rating created/updated successfully!")
        print(f"   Rating Score: {rating.rating_score:.2f}")
        print(f"   Total Points: {rating.total_points:.1f}")
        print(f"   Payment Days: {rating.total_payment_days}")
        print(f"   Is Active: {rating.is_active}")
        print(f"   Consecutive Non-payment Days: {rating.consecutive_non_payment_days}")
    except Exception as e:
        print(f"❌ Error creating/updating rating: {str(e)}")
        return
    
    # Test 6: Test missed revenue creation
    print("\n📝 Testing missed revenue creation...")
    try:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=7)
        created_count = create_missed_revenue_records(partner, start_date, end_date)
        print(f"✅ Created {created_count} missed revenue records")
    except Exception as e:
        print(f"❌ Error creating missed revenue: {str(e)}")
    
    # Test 7: Check existing ratings
    print("\n📊 Checking existing ratings...")
    ratings = DriverRating.objects.filter(partner=partner)
    print(f"📈 Found {ratings.count()} driver ratings for this partner")
    
    for rating in ratings:
        print(f"   {rating.driver.first_name} {rating.driver.last_name} - {rating.vehicle.registration_number}: {rating.rating_score:.2f}")
    
    # Test 8: Test rating calculation with specific dates
    print("\n🧮 Testing rating calculation with specific dates...")
    try:
        rating = calculate_driver_rating(driver, vehicle, end_date, start_date)
        print(f"✅ Rating calculated successfully!")
        print(f"   Rating Score: {rating.rating_score:.2f}")
        print(f"   Total Points: {rating.total_points:.1f}")
        print(f"   Payment Days: {rating.total_payment_days}")
    except Exception as e:
        print(f"❌ Error calculating rating: {str(e)}")
    
    print("\n🎉 Driver Rating System Test Completed!")
    print("=" * 50)


def test_management_command():
    """Test the management command functionality"""
    print("\n🔧 Testing Management Command")
    print("=" * 50)
    
    from django.core.management import call_command
    from io import StringIO
    
    try:
        # Test dry run of the management command
        out = StringIO()
        call_command('manage_driver_ratings', '--action', 'check_missed_payments', '--dry-run', stdout=out)
        print("✅ Management command executed successfully (dry run)")
        print("Output:")
        print(out.getvalue())
    except Exception as e:
        print(f"❌ Error running management command: {str(e)}")


if __name__ == "__main__":
    print("🧪 Driver Rating System Test Suite")
    print("=" * 50)
    
    try:
        test_driver_rating_system()
        test_management_command()
    except Exception as e:
        print(f"❌ Test suite failed: {str(e)}")
        import traceback
        traceback.print_exc() 