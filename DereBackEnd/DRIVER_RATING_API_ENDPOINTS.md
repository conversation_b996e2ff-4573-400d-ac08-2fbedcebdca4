# Driver Rating API Endpoints

This document provides comprehensive documentation for all API endpoints related to the driver rating system.

## Authentication

All rating endpoints require authentication. Include the JWT token in the Authorization header:

```http
Authorization: Bearer <your_jwt_token>
```

## Base URL

All endpoints are prefixed with `/api/`

## Driver Rating Endpoints

### 1. Get Driver Rating Dashboard

Get comprehensive rating information for a specific driver.

**Endpoint:** `GET /api/drivers/{driver_id}/rating/`

**Authentication:** Required
- Drivers can only view their own ratings
- Partners can view their drivers' ratings
- Admins can view all ratings

#### Request
```http
GET /api/drivers/123/rating/
Authorization: Bearer <token>
```

#### Response (200 OK)
```json
{
    "driver_id": 123,
    "driver_name": "<PERSON>",
    "driver_code": "DRV-0123",
    "age_group": "25-40",
    "current_rating": 4.25,
    "rating_level": "Excellent",
    "total_points": 127.5,
    "total_payment_days": 30,
    "payment_completion_rate": 85.5,
    "consecutive_non_payment_days": 0,
    "is_active": true,
    "last_payment_date": "2024-01-15",
    "calculation_start_date": "2023-12-01",
    "last_updated": "2024-01-16T10:30:00Z",
    "vehicle_ratings": [
        {
            "vehicle_id": 1,
            "vehicle_registration": "KCA 123X",
            "rating_score": 4.5,
            "total_points": 67.5,
            "payment_days": 15,
            "last_payment_date": "2024-01-15",
            "is_active": true
        },
        {
            "vehicle_id": 2,
            "vehicle_registration": "KCB 456Y",
            "rating_score": 4.0,
            "total_points": 60.0,
            "payment_days": 15,
            "last_payment_date": "2024-01-14",
            "is_active": true
        }
    ],
    "rating_breakdown": {
        "on_time_payments": 20,
        "late_payments": 8,
        "missed_payments": 2,
        "on_time_percentage": 66.7,
        "late_percentage": 26.7,
        "missed_percentage": 6.7
    }
}
```

#### Response (No Ratings)
```json
{
    "driver_id": 123,
    "driver_name": "John Doe",
    "driver_code": "DRV-0123",
    "age_group": "25-40",
    "current_rating": 0.0,
    "rating_level": "No Rating",
    "message": "No ratings available yet. Ratings are calculated based on payment behavior."
}
```

#### Error Responses
- `401 Unauthorized`: Missing or invalid token
- `403 Forbidden`: Driver trying to access another driver's rating
- `404 Not Found`: Driver not found

### 2. Get Driver Rating Trend

Get rating trend data over time for a specific driver.

**Endpoint:** `GET /api/drivers/{driver_id}/rating_trend/`

**Authentication:** Required (same permissions as rating dashboard)

#### Request Parameters
- `period` (optional): `weekly` or `monthly` (default: `weekly`)
- `start_date` (optional): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format

#### Request Examples
```http
# Weekly trend (default)
GET /api/drivers/123/rating_trend/

# Monthly trend
GET /api/drivers/123/rating_trend/?period=monthly

# Custom date range
GET /api/drivers/123/rating_trend/?period=weekly&start_date=2024-01-01&end_date=2024-01-31
```

#### Response (200 OK)
```json
{
    "driver_id": 123,
    "driver_name": "John Doe",
    "period": "weekly",
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "trend_data": [
        {
            "period_label": "Week 1 (Jan 1-7)",
            "start_date": "2024-01-01",
            "end_date": "2024-01-07",
            "rating_score": 4.5,
            "total_points": 22.5,
            "payment_days": 5,
            "on_time_payments": 4,
            "late_payments": 1,
            "missed_payments": 0
        },
        {
            "period_label": "Week 2 (Jan 8-14)",
            "start_date": "2024-01-08",
            "end_date": "2024-01-14",
            "rating_score": 4.0,
            "total_points": 20.0,
            "payment_days": 5,
            "on_time_payments": 3,
            "late_payments": 2,
            "missed_payments": 0
        }
    ],
    "summary": {
        "average_rating": 4.25,
        "total_points": 127.5,
        "total_payment_days": 30,
        "trend_direction": "stable",
        "improvement_percentage": 2.5
    }
}
```

### 3. Manual Rating Calculation

Trigger manual rating calculation for a specific driver.

**Endpoint:** `POST /api/drivers/{driver_id}/calculate_rating/`

**Authentication:** Required
- Drivers can calculate their own ratings
- Partners can calculate their drivers' ratings

#### Request Body (Optional)
```json
{
    "vehicle_id": 1,  // Optional: calculate for specific vehicle only
    "recalculate_from_date": "2024-01-01"  // Optional: recalculate from specific date
}
```

#### Request Examples
```http
# Calculate all ratings for driver
POST /api/drivers/123/calculate_rating/
Authorization: Bearer <token>
Content-Type: application/json
{}

# Calculate for specific vehicle
POST /api/drivers/123/calculate_rating/
Authorization: Bearer <token>
Content-Type: application/json
{
    "vehicle_id": 1
}

# Recalculate from specific date
POST /api/drivers/123/calculate_rating/
Authorization: Bearer <token>
Content-Type: application/json
{
    "recalculate_from_date": "2024-01-01"
}
```

#### Response (200 OK)
```json
{
    "message": "Driver ratings calculated successfully",
    "driver_id": 123,
    "driver_name": "John Doe",
    "results": [
        {
            "vehicle_id": 1,
            "vehicle_registration": "KCA 123X",
            "rating_updated": true,
            "current_rating": 4.5,
            "total_points": 67.5,
            "payment_days": 15,
            "previous_rating": 4.2
        },
        {
            "vehicle_id": 2,
            "vehicle_registration": "KCB 456Y",
            "rating_updated": true,
            "current_rating": 4.0,
            "total_points": 60.0,
            "payment_days": 15,
            "previous_rating": 3.8
        }
    ],
    "calculation_date": "2024-01-16T10:30:00Z"
}
```

#### Error Responses
- `404 Not Found`: Vehicle not found (when vehicle_id specified)
- `400 Bad Request`: Invalid date format

## Partner Rating Endpoints

### 1. Recalculate All Driver Ratings

Recalculate ratings for all drivers under a partner.

**Endpoint:** `POST /api/partner-drivers/recalculate_all_driver_ratings/`

**Authentication:** Required (Partner or Admin role only)

#### Request Body (Optional)
```json
{
    "recalculate_from_date": "2024-01-01",  // Optional: recalculate from specific date
    "vehicle_id": 1,  // Optional: calculate for specific vehicle only
    "driver_id": 123  // Optional: calculate for specific driver only
}
```

#### Request Examples
```http
# Recalculate all drivers
POST /api/partner-drivers/recalculate_all_driver_ratings/
Authorization: Bearer <token>
Content-Type: application/json
{}

# Recalculate from specific date
POST /api/partner-drivers/recalculate_all_driver_ratings/
Authorization: Bearer <token>
Content-Type: application/json
{
    "recalculate_from_date": "2024-01-01"
}

# Recalculate specific vehicle across all drivers
POST /api/partner-drivers/recalculate_all_driver_ratings/
Authorization: Bearer <token>
Content-Type: application/json
{
    "vehicle_id": 1
}
```

#### Response (200 OK)
```json
{
    "message": "Driver ratings recalculated successfully",
    "partner_id": 1,
    "partner_name": "ABC Transport",
    "total_drivers_processed": 5,
    "total_ratings_updated": 12,
    "recalculate_from_date": "2024-01-01",
    "calculation_date": "2024-01-16T10:30:00Z",
    "results": [
        {
            "driver_id": 123,
            "driver_name": "John Doe",
            "vehicle_id": 1,
            "vehicle_registration": "KCA 123X",
            "rating_updated": true,
            "current_rating": 4.5,
            "total_points": 67.5,
            "payment_days": 15
        },
        {
            "driver_id": 124,
            "driver_name": "Jane Smith",
            "vehicle_id": 2,
            "vehicle_registration": "KCB 456Y",
            "rating_updated": true,
            "current_rating": 3.8,
            "total_points": 57.0,
            "payment_days": 15
        }
    ]
}
```

#### Error Responses
- `403 Forbidden`: Non-partner trying to access endpoint
- `404 Not Found`: Partner profile not found

## Rating Filtering and Pagination

### Query Parameters for List Endpoints

When applicable, endpoints support filtering and pagination:

#### Filtering Parameters
- `date_from`: Filter ratings from this date (YYYY-MM-DD)
- `date_to`: Filter ratings to this date (YYYY-MM-DD)
- `vehicle_id`: Filter by specific vehicle
- `is_active`: Filter by active status (true/false)
- `min_rating`: Minimum rating score
- `max_rating`: Maximum rating score
- `age_group`: Filter by driver age group

#### Pagination Parameters
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

#### Sorting Parameters
- `ordering`: Sort field (rating_score, last_updated, total_points)
- Add `-` prefix for descending order

#### Example with Filters
```http
GET /api/drivers/123/rating_trend/?period=monthly&date_from=2024-01-01&date_to=2024-03-31&ordering=-rating_score
```

## Error Handling

### Common Error Responses

#### 400 Bad Request
```json
{
    "detail": "Invalid date format. Use YYYY-MM-DD.",
    "code": "invalid_date_format"
}
```

#### 401 Unauthorized
```json
{
    "detail": "Authentication credentials were not provided.",
    "code": "not_authenticated"
}
```

#### 403 Forbidden
```json
{
    "detail": "You can only view your own rating.",
    "code": "permission_denied"
}
```

#### 404 Not Found
```json
{
    "detail": "Driver not found.",
    "code": "not_found"
}
```

#### 500 Internal Server Error
```json
{
    "detail": "Failed to calculate rating. Please try again later.",
    "code": "calculation_error"
}
```

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Driver endpoints**: 100 requests per minute per user
- **Partner endpoints**: 200 requests per minute per user
- **Calculation endpoints**: 10 requests per minute per user

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```

## SDK Examples

### JavaScript/Node.js
```javascript
// Get driver rating
const response = await fetch('/api/drivers/123/rating/', {
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    }
});
const rating = await response.json();

// Calculate rating
const calcResponse = await fetch('/api/drivers/123/calculate_rating/', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        vehicle_id: 1
    })
});
```

### Python
```python
import requests

# Get driver rating
headers = {'Authorization': f'Bearer {token}'}
response = requests.get('/api/drivers/123/rating/', headers=headers)
rating = response.json()

# Calculate rating
calc_response = requests.post(
    '/api/drivers/123/calculate_rating/',
    headers=headers,
    json={'vehicle_id': 1}
)
```

### cURL
```bash
# Get driver rating
curl -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     /api/drivers/123/rating/

# Calculate rating
curl -X POST \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"vehicle_id": 1}' \
     /api/drivers/123/calculate_rating/
```

This comprehensive API documentation covers all driver rating endpoints with detailed examples and error handling information.
