# Driver Rating System - Comprehensive Testing Guide

This guide covers all aspects of testing the driver rating functionality, including calculation from fresh data, management commands, and API endpoints.

## Overview

The driver rating system calculates ratings based on payment behavior:
- **5.0 points**: On-time payments (before deadline)
- **2.5 points**: Late payments (after deadline but made)
- **0.0 points**: Missed payments (no payment made)

Rating Score = Total Points ÷ Total Expected Payment Days

## Test Structure

### 1. Core Test Classes

- **TestDriverRatingCalculation**: Core rating calculation logic
- **TestDriverRatingManagementCommands**: Management command functionality  
- **TestDriverRatingAPIEndpoints**: All API endpoints
- **TestDriverRatingPermissions**: Authentication and authorization
- **TestDriverRatingEdgeCases**: Edge cases and error scenarios
- **TestDriverRatingIntegration**: End-to-end integration tests

## Running Tests

### Run All Rating Tests
```bash
cd DereBackEnd
python manage.py test akuko_api.tests
```

### Run Specific Test Classes
```bash
# Core calculation tests
python manage.py test akuko_api.tests.TestDriverRatingCalculation

# Management command tests
python manage.py test akuko_api.tests.TestDriverRatingManagementCommands

# API endpoint tests
python manage.py test akuko_api.tests.TestDriverRatingAPIEndpoints

# Permission tests
python manage.py test akuko_api.tests.TestDriverRatingPermissions

# Edge case tests
python manage.py test akuko_api.tests.TestDriverRatingEdgeCases

# Integration tests
python manage.py test akuko_api.tests.TestDriverRatingIntegration
```

### Run Specific Test Methods
```bash
# Test fresh rating calculation
python manage.py test akuko_api.tests.TestDriverRatingCalculation.test_fresh_rating_calculation_perfect_payments

# Test management command
python manage.py test akuko_api.tests.TestDriverRatingManagementCommands.test_management_command_calculate_ratings

# Test API endpoint
python manage.py test akuko_api.tests.TestDriverRatingAPIEndpoints.test_driver_rating_endpoint_as_driver
```

## Rating Calculation from Fresh Data

### Basic Calculation Process

1. **Create Test Data**:
   ```python
   # Partner, Driver, Vehicle, PaymentSettings
   partner = Partner.objects.create(...)
   driver = Driver.objects.create(partner=partner, ...)
   vehicle = Vehicle.objects.create(partner=partner, driver=driver, ...)
   payment_settings = PaymentSettings.objects.create(
       partner=partner,
       vehicle=vehicle,
       payment_days='Monday,Wednesday,Friday',
       deadline_time=time(17, 0)
   )
   ```

2. **Create Revenue Records**:
   ```python
   # On-time payment
   Revenue.objects.create(
       driver=driver,
       vehicle=vehicle,
       date=date(2024, 1, 1),  # Monday
       amount=Decimal('1000.00'),
       status='on_time'
   )
   
   # Late payment
   Revenue.objects.create(
       driver=driver,
       vehicle=vehicle,
       date=date(2024, 1, 3),  # Wednesday
       amount=Decimal('1000.00'),
       status='late'
   )
   
   # Missed payment (no record or status='missed')
   ```

3. **Calculate Rating**:
   ```python
   from akuko_api.rating_utils import calculate_driver_rating
   
   rating = calculate_driver_rating(driver, vehicle, end_date)
   
   print(f"Rating Score: {rating.rating_score}")
   print(f"Total Points: {rating.total_points}")
   print(f"Payment Days: {rating.total_payment_days}")
   print(f"Is Active: {rating.is_active}")
   ```

### Test Scenarios Covered

1. **Perfect Payments**: All payments on time → Rating = 5.0
2. **Mixed Payments**: Combination of on-time, late, missed → Calculated average
3. **No Payments**: No revenue records → Rating = 0.0, inactive after 3+ days
4. **Consecutive Missed**: 3+ consecutive missed payments → Rating becomes inactive
5. **Reactivation**: Payment after being inactive → Rating reactivates

## Management Commands

### Available Commands

The `manage_driver_ratings` command supports multiple actions:

```bash
python manage.py manage_driver_ratings --help
```

### Command Actions

#### 1. Check Missed Payments
```bash
# Create missed revenue records for all partners
python manage.py manage_driver_ratings --action check_missed_payments --days 7

# For specific partner
python manage.py manage_driver_ratings --action check_missed_payments --partner-id 1 --days 7

# With date range
python manage.py manage_driver_ratings --action check_missed_payments --start-date 2024-01-01 --end-date 2024-01-07

# Dry run (no changes)
python manage.py manage_driver_ratings --action check_missed_payments --dry-run
```

#### 2. Calculate Ratings
```bash
# Calculate ratings for all partners
python manage.py manage_driver_ratings --action calculate_ratings

# For specific partner
python manage.py manage_driver_ratings --action calculate_ratings --partner-id 1

# With recalculation from specific date
python manage.py manage_driver_ratings --action calculate_ratings --recalculate-from 2024-01-01
```

#### 3. Full Synchronization
```bash
# Complete sync: missed payments + rating calculation
python manage.py manage_driver_ratings --action full_sync --partner-id 1 --days 30
```

### Testing Management Commands

```python
def test_management_command_dry_run(self):
    """Test management command in dry-run mode"""
    out = StringIO()
    
    call_command(
        'manage_driver_ratings',
        '--action', 'check_missed_payments',
        '--dry-run',
        stdout=out
    )
    
    output = out.getvalue()
    self.assertIn('DRY RUN', output)
    
    # Verify no changes were made
    revenue_count = Revenue.objects.filter(vehicle=self.vehicle).count()
    self.assertEqual(revenue_count, 0)
```

## API Endpoints

### Driver Rating Endpoints

#### 1. Get Driver Rating Dashboard
```http
GET /api/drivers/{driver_id}/rating/
Authorization: Bearer {token}
```

**Response**:
```json
{
    "driver_id": 1,
    "driver_name": "John Doe",
    "driver_code": "DRV-0001",
    "age_group": "25-40",
    "current_rating": 4.25,
    "rating_level": "Excellent",
    "total_points": 127.5,
    "total_payment_days": 30,
    "payment_completion_rate": 85.5,
    "vehicle_ratings": [
        {
            "vehicle_id": 1,
            "registration": "KCA 123X",
            "rating_score": 4.5,
            "total_points": 67.5,
            "payment_days": 15
        }
    ]
}
```

#### 2. Get Rating Trend
```http
GET /api/drivers/{driver_id}/rating_trend/?period=weekly
Authorization: Bearer {token}
```

**Parameters**:
- `period`: `weekly` or `monthly`
- `start_date`: Optional start date (YYYY-MM-DD)
- `end_date`: Optional end date (YYYY-MM-DD)

#### 3. Manual Rating Calculation
```http
POST /api/drivers/{driver_id}/calculate_rating/
Authorization: Bearer {token}
Content-Type: application/json

{
    "vehicle_id": 1,  // Optional: calculate for specific vehicle
    "recalculate_from_date": "2024-01-01"  // Optional: recalculate from date
}
```

### Partner Rating Endpoints

#### 1. Recalculate All Driver Ratings
```http
POST /api/partner-drivers/recalculate_all_driver_ratings/
Authorization: Bearer {token}
Content-Type: application/json

{
    "recalculate_from_date": "2024-01-01",  // Optional
    "vehicle_id": 1  // Optional: specific vehicle only
}
```

### Testing API Endpoints

```python
def test_driver_rating_endpoint_as_driver(self):
    """Test driver rating endpoint when accessed by the driver themselves"""
    self.client.force_authenticate(user=self.driver_user)
    
    url = f'/api/drivers/{self.driver.id}/rating/'
    response = self.client.get(url)
    
    self.assertEqual(response.status_code, status.HTTP_200_OK)
    data = response.json()
    
    # Verify response structure
    self.assertIn('driver_id', data)
    self.assertIn('current_rating', data)
    self.assertIn('rating_level', data)
```

## Permission Testing

### Authentication Requirements

- **Drivers**: Can only view their own ratings
- **Partners**: Can view their drivers' ratings and recalculate ratings
- **Admins**: Full access to all rating functionality

### Testing Permissions

```python
def test_driver_can_only_view_own_rating(self):
    """Test that drivers can only view their own ratings"""
    self.client.force_authenticate(user=self.driver1_user)
    
    # Should work for own rating
    url = f'/api/drivers/{self.driver1.id}/rating/'
    response = self.client.get(url)
    self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    # Should fail for other driver's rating
    url = f'/api/drivers/{self.driver2.id}/rating/'
    response = self.client.get(url)
    self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
```

## Edge Cases and Error Handling

### Common Edge Cases Tested

1. **No Payment Settings**: Rating calculation with no payment configuration
2. **Future Dates**: Calculation with future end dates
3. **Missing Birth Date**: Age group calculation for drivers without birth date
4. **Large Amounts**: Very large payment amounts
5. **Zero Amounts**: Zero amount payments
6. **Partner Transfer**: Driver switching partners (ratings continue)
7. **Concurrent Calculations**: Multiple simultaneous rating calculations

### Error Scenarios

1. **Invalid Vehicle ID**: Non-existent vehicle in API calls
2. **Unauthorized Access**: Wrong user accessing ratings
3. **Invalid Date Ranges**: Malformed dates in requests
4. **Missing Required Data**: Calculations with incomplete data

## Integration Testing

### End-to-End Workflow Test

1. Create test data (partners, drivers, vehicles, payment settings)
2. Generate revenue records with mixed payment statuses
3. Run management commands to create missed payments
4. Calculate ratings via management command
5. Verify ratings through API endpoints
6. Test filtering and trend analysis
7. Test manual recalculation

### Data Consistency Verification

- Rating scores match expected calculations
- Total points and payment days are accurate
- Active/inactive status is correct
- Partner transfers preserve rating history
- Concurrent operations don't corrupt data

## Best Practices for Testing

1. **Use Transactions**: Wrap tests in transactions for cleanup
2. **Test Permissions**: Always test authentication and authorization
3. **Verify Calculations**: Check math manually for key test cases
4. **Test Edge Cases**: Include boundary conditions and error scenarios
5. **Integration Tests**: Test complete workflows end-to-end
6. **Performance**: Test with realistic data volumes
7. **Cleanup**: Ensure tests clean up after themselves

## Troubleshooting

### Common Issues

1. **Rating Not Updating**: Check if rating is inactive due to consecutive missed payments
2. **Incorrect Calculations**: Verify payment settings and expected payment days
3. **Permission Errors**: Ensure correct user authentication and role
4. **Missing Data**: Check that payment settings and revenue records exist

### Debug Commands

```bash
# Check rating status
python manage.py shell
>>> from akuko_api.models import DriverRating
>>> rating = DriverRating.objects.get(driver_id=1, vehicle_id=1)
>>> print(f"Active: {rating.is_active}, Score: {rating.rating_score}")

# Recalculate specific rating
>>> from akuko_api.rating_utils import calculate_driver_rating
>>> rating = calculate_driver_rating(driver, vehicle)
>>> print(f"Updated score: {rating.rating_score}")
```

This comprehensive testing guide covers all aspects of the driver rating system. Use the provided test classes and examples to ensure the rating functionality works correctly in all scenarios.
