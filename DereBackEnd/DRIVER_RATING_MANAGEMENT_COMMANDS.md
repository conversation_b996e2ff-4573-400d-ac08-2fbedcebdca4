# Driver Rating Management Commands

This document provides comprehensive documentation for all management commands related to the driver rating system.

## Overview

The `manage_driver_ratings` command is the primary management command for handling driver rating operations. It supports multiple actions for creating missed payment records, calculating ratings, and performing full synchronization.

## Command Syntax

```bash
python manage.py manage_driver_ratings [OPTIONS]
```

## Available Actions

### 1. Check Missed Payments (`check_missed_payments`)

Creates missed revenue records for expected payment days when no payment was made.

#### Basic Usage
```bash
# Create missed payments for all partners (last 7 days)
python manage.py manage_driver_ratings --action check_missed_payments --days 7

# For specific partner
python manage.py manage_driver_ratings --action check_missed_payments --partner-id 1 --days 7

# Dry run (preview changes without making them)
python manage.py manage_driver_ratings --action check_missed_payments --dry-run --days 7
```

#### With Date Range
```bash
# Specific date range
python manage.py manage_driver_ratings \
    --action check_missed_payments \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --partner-id 1

# Last 30 days for all partners
python manage.py manage_driver_ratings --action check_missed_payments --days 30
```

#### Parameters
- `--partner-id`: Process specific partner only
- `--days`: Number of days back from today
- `--start-date`: Start date (YYYY-MM-DD format)
- `--end-date`: End date (YYYY-MM-DD format)
- `--dry-run`: Preview changes without executing

### 2. Calculate Ratings (`calculate_ratings`)

Calculate or recalculate driver ratings based on existing revenue data.

#### Basic Usage
```bash
# Calculate ratings for all partners
python manage.py manage_driver_ratings --action calculate_ratings

# For specific partner
python manage.py manage_driver_ratings --action calculate_ratings --partner-id 1

# Recalculate from specific date
python manage.py manage_driver_ratings \
    --action calculate_ratings \
    --partner-id 1 \
    --recalculate-from 2024-01-01
```

#### Parameters
- `--partner-id`: Process specific partner only
- `--recalculate-from`: Recalculate ratings from this date (YYYY-MM-DD)
- `--vehicle-id`: Calculate for specific vehicle only
- `--driver-id`: Calculate for specific driver only

### 3. Full Synchronization (`full_sync`)

Performs complete synchronization: creates missed payments and calculates ratings.

#### Basic Usage
```bash
# Full sync for specific partner (last 30 days)
python manage.py manage_driver_ratings \
    --action full_sync \
    --partner-id 1 \
    --days 30

# Full sync with date range
python manage.py manage_driver_ratings \
    --action full_sync \
    --partner-id 1 \
    --start-date 2024-01-01 \
    --end-date 2024-01-31
```

#### Parameters
Combines all parameters from `check_missed_payments` and `calculate_ratings`.

### 4. Cleanup Operations (`cleanup`)

Remove invalid or duplicate rating records.

#### Basic Usage
```bash
# Clean up duplicate ratings
python manage.py manage_driver_ratings --action cleanup --partner-id 1

# Remove inactive ratings older than 6 months
python manage.py manage_driver_ratings \
    --action cleanup \
    --remove-inactive \
    --older-than 180
```

## Command Parameters Reference

### Required Parameters
- `--action`: The action to perform (`check_missed_payments`, `calculate_ratings`, `full_sync`, `cleanup`)

### Optional Parameters
- `--partner-id INTEGER`: Process specific partner only
- `--driver-id INTEGER`: Process specific driver only
- `--vehicle-id INTEGER`: Process specific vehicle only
- `--days INTEGER`: Number of days back from today
- `--start-date DATE`: Start date in YYYY-MM-DD format
- `--end-date DATE`: End date in YYYY-MM-DD format
- `--recalculate-from DATE`: Recalculate ratings from this date
- `--dry-run`: Preview changes without executing them
- `--verbose`: Show detailed output
- `--force`: Force operation even if data exists
- `--batch-size INTEGER`: Process records in batches (default: 100)

## Usage Examples

### Daily Maintenance
```bash
# Daily job to create missed payments and update ratings
python manage.py manage_driver_ratings --action full_sync --days 1
```

### Weekly Cleanup
```bash
# Weekly job to process last 7 days
python manage.py manage_driver_ratings --action full_sync --days 7 --verbose
```

### Monthly Recalculation
```bash
# Monthly recalculation for specific partner
python manage.py manage_driver_ratings \
    --action calculate_ratings \
    --partner-id 1 \
    --recalculate-from 2024-01-01 \
    --verbose
```

### Specific Vehicle Processing
```bash
# Process specific vehicle only
python manage.py manage_driver_ratings \
    --action full_sync \
    --vehicle-id 123 \
    --days 30
```

### Data Migration/Import
```bash
# After importing historical data
python manage.py manage_driver_ratings \
    --action full_sync \
    --start-date 2023-01-01 \
    --end-date 2024-01-31 \
    --force
```

## Output Examples

### Successful Execution
```
Processing partner: ABC Transport (ID: 1)
Created 15 missed revenue records
Updated 8 driver ratings
Processed 3 vehicles
Total drivers processed: 5
Execution time: 2.3 seconds
```

### Dry Run Output
```
DRY RUN MODE - No changes will be made

Would process partner: ABC Transport (ID: 1)
Would create 15 missed revenue records
Would update 8 driver ratings
Would process 3 vehicles
Total drivers that would be processed: 5
```

### Error Handling
```
Error processing partner 1: Payment settings not found for vehicle 123
Warning: Driver 456 has no revenue records
Skipping inactive driver 789
Successfully processed 4 out of 7 drivers
```

## Scheduling Commands

### Cron Job Examples
```bash
# Daily at 2 AM - process yesterday's data
0 2 * * * cd /path/to/project && python manage.py manage_driver_ratings --action full_sync --days 1

# Weekly on Sunday at 3 AM - process last week
0 3 * * 0 cd /path/to/project && python manage.py manage_driver_ratings --action full_sync --days 7

# Monthly on 1st at 4 AM - recalculate all ratings
0 4 1 * * cd /path/to/project && python manage.py manage_driver_ratings --action calculate_ratings --recalculate-from $(date -d "1 month ago" +%Y-%m-01)
```

### Django Management Command in Code
```python
from django.core.management import call_command
from io import StringIO

# Capture output
out = StringIO()
call_command(
    'manage_driver_ratings',
    '--action', 'full_sync',
    '--partner-id', 1,
    '--days', 7,
    stdout=out
)
output = out.getvalue()
```

## Best Practices

1. **Always test with --dry-run first**
2. **Use specific partner-id for large datasets**
3. **Schedule regular maintenance jobs**
4. **Monitor execution time and optimize batch-size**
5. **Keep logs of command executions**
6. **Use date ranges for historical data processing**
7. **Backup database before major operations**

## Troubleshooting

### Common Issues

1. **No payment settings found**
   - Ensure PaymentSettings exist for vehicles
   - Check partner-vehicle relationships

2. **Memory issues with large datasets**
   - Use smaller batch sizes
   - Process by date ranges
   - Use specific partner/vehicle filters

3. **Duplicate ratings**
   - Run cleanup action
   - Check for concurrent executions

4. **Performance issues**
   - Add database indexes
   - Use batch processing
   - Schedule during off-peak hours

### Debug Commands
```bash
# Check what would be processed
python manage.py manage_driver_ratings --action check_missed_payments --dry-run --verbose

# Process single vehicle for testing
python manage.py manage_driver_ratings --action full_sync --vehicle-id 1 --days 7 --verbose
```

This comprehensive guide covers all aspects of the driver rating management commands. Use these commands to maintain accurate and up-to-date driver ratings in your system.
