from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
import uuid
from django.db import models
from django.utils import timezone
from datetime import timedelta
from django.core.exceptions import ValidationError


class LowercaseEmailField(models.EmailField):
    """
    Override EmailField to convert emails to lowercase before saving.
    """

    def to_python(self, value):
        """
        Convert email to lowercase before saving it to the database.
        """
        value = super().to_python(value)
        if isinstance(value, str):
            return value.lower()
        return value


class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email).lower()
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)


# Users Model
class User(AbstractBaseUser):
    email = LowercaseEmailField(max_length=255, unique=True)
    first_name = models.CharField(max_length=30, null=True, blank=True)
    last_name = models.CharField(max_length=30, null=True, blank=True)
    role = models.CharField(max_length=10, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(
        auto_now_add=True, null=True, blank=True)
    password = models.CharField(max_length=128, blank=True, null=True)
    email_verified = models.BooleanField(default=False)
    verification_code = models.CharField(
        max_length=36, unique=True, default=uuid.uuid4, editable=False)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    notification_subscribe = models.BooleanField(
        default=True)  # New field for notification subscription

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.email

    def has_perm(self, perm, obj=None):
        return self.is_superuser

    def has_module_perms(self, app_label):
        return self.is_superuser

# Vehicle Make
class VehicleMake(models.Model):
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name


# vehicle Model
class VehicleModel(models.Model):
    name = models.CharField(max_length=100)
    make = models.ForeignKey(
        VehicleMake, related_name='models', on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.make.name} {self.name}"


class VehicleType(models.Model):
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name


class WorkArea(models.Model):
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name


# Partner Model
class Partner(models.Model):
    user = models.OneToOneField(User, null=True, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=30, null=True, blank=True)
    last_name = models.CharField(max_length=30, null=True, blank=True)
    email = models.EmailField(max_length=255, null=True, blank=True)
    id_number = models.CharField(max_length=20, null=True, blank=True)
    company_number = models.CharField(max_length=20, blank=True, null=True)
    company_name = models.CharField(max_length=100, blank=True, null=True)
    password = models.CharField(max_length=128, null=True, blank=True)
    confirm_password = models.CharField(max_length=128, null=True, blank=True)
    mobile_number = models.CharField(max_length=15, null=True, blank=True)
    id_photo = models.FileField(upload_to='photos/id/', blank=True, null=True)
    car_photo = models.FileField(
        upload_to='photos/car/', blank=True, null=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.id})"

# Driver Model
class Driver(models.Model):

    MALE = 'M'
    FEMALE = 'F'
    GENDER_CHOICES = [
        (MALE, 'Male'),
        (FEMALE, 'Female'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=30, null=True, blank=True)
    last_name = models.CharField(max_length=30, null=True, blank=True)
    email = models.EmailField(max_length=255, null=True, blank=True)
    partner = models.ForeignKey(
        Partner, on_delete=models.SET_NULL, null=True, blank=True, related_name="drivers")
    id_number = models.CharField(max_length=20, null=True, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(
        max_length=10, choices=GENDER_CHOICES, null=True, blank=True)
    mobile_number = models.CharField(max_length=15, null=True, blank=True)
    password = models.CharField(max_length=128, null=True, blank=True)
    confirm_password = models.CharField(max_length=128, null=True, blank=True)
    vehicle_type = models.ForeignKey(
        VehicleType, on_delete=models.SET_NULL, null=True, blank=True)
    work_area = models.ForeignKey(
        WorkArea, on_delete=models.SET_NULL, null=True, blank=True)
    psv_photo = models.FileField(
        upload_to='photos/psv/', blank=True, null=True)
    id_photo = models.FileField(upload_to='photos/id/', blank=True, null=True)
    license_photo = models.FileField(
        upload_to='photos/license/', blank=True, null=True)
    good_conduct_photo = models.FileField(
        upload_to='photos/good_conduct/', blank=True, null=True)
    uber_trips_screenshot = models.FileField(
        upload_to='photos/uber_trips/', blank=True, null=True,
        help_text='Upload a screenshot of your Uber trips (JPG, PNG, or PDF format, max 5MB)')
    license_expiry_date = models.DateField(
        blank=True, null=True,
        help_text='Enter your driving license expiry date')
    psv_expiry_date = models.DateField(
        blank=True, null=True,
        help_text='Enter your PSV license expiry date')
    good_conduct_expiry_date = models.DateField(
        blank=True, null=True,
        help_text='Enter your good conduct certificate expiry date')

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"

# Job Model
class Job(models.Model):
    status_choices = [
        ('open', 'Open'),
        ('closed', 'Closed'),
    ]

    status = models.CharField(
        max_length=10, choices=status_choices, default='open')
    partner = models.ForeignKey(
        Partner, related_name='jobs', on_delete=models.CASCADE)
    vehicle_make = models.ForeignKey(
        VehicleMake, on_delete=models.SET_NULL, null=True)
    vehicle_model = models.ForeignKey(
        VehicleModel, on_delete=models.SET_NULL, null=True)
    preferred_work_area = models.ForeignKey(
        WorkArea, on_delete=models.SET_NULL, null=True)
    requirements = models.TextField()
    vehicle_photo = models.FileField(
        upload_to='photos/vehicles/', null=True, blank=True)
    work_days = models.CharField(max_length=100)
    min_age = models.IntegerField(null=True, blank=True)
    max_age = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, blank=True)
    closed_at = models.DateTimeField(auto_now_add=True, blank=True)

    @property
    def application_count(self):
        return self.applications.count()

    def __str__(self):
        return f"Job posted by {self.partner}  for {self.vehicle_model} in {self.preferred_work_area}"

# JobApplication Model


class JobApplication(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('rejected', 'Rejected'),
        ('withdrawn', 'Withdrawn'),
        ('hired', 'Hired'),
        ('unhired', 'Unhired'),
    ]

    job = models.ForeignKey(Job, on_delete=models.CASCADE,
                            related_name='applications')
    driver = models.ForeignKey(Driver, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=10, choices=STATUS_CHOICES, default='active')
    removed_by_partner = models.BooleanField(default=False)
    withdrawn_by_driver = models.BooleanField(default=False)
    applied_at = models.DateTimeField(auto_now_add=True)
    hired_at = models.DateTimeField(null=True, blank=True)
    un_hired_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.driver} applied for {self.job} - Status: {self.status}"

    def save(self, *args, **kwargs):
        # Sync the status with the flags and ensure proper transition when hired/unhired
        if self.removed_by_partner:
            self.status = 'rejected'
        elif self.withdrawn_by_driver:
            self.status = 'withdrawn'
        elif self.status == 'hired':
            self.hired_at = self.hired_at or timezone.now()  # Set hired_at if not already set
            self.un_hired_at = None  # Clear un_hired_at
        elif self.status == 'unhired':
            # Set un_hired_at if not already set
            self.un_hired_at = self.un_hired_at or timezone.now()
            self.hired_at = None  # Clear hired_at
        else:
            self.status = 'active'

        super().save(*args, **kwargs)


# Forgot password
class PasswordResetToken(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    token = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def is_expired(self):
        expired = timezone.now() > self.created_at + timedelta(minutes=720)
        print(
            f"Token expired check: {expired}, Created at: {self.created_at}, Now: {timezone.now()}")
        return expired


# Vehicle Model
class Vehicle(models.Model):

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]

    partner = models.ForeignKey(
        'Partner', related_name='vehicles', on_delete=models.CASCADE)
    driver = models.ForeignKey(
        'Driver', on_delete=models.SET_NULL, null=True, blank=True, related_name='vehicle')
    registration_number = models.CharField(max_length=20, unique=True, blank=True, null=True)
    vehicle_make = models.ForeignKey(
        'VehicleMake', on_delete=models.SET_NULL, null=True)
    vehicle_model = models.ForeignKey(
        'VehicleModel', on_delete=models.SET_NULL, null=True)
    preferred_work_area = models.ForeignKey(
        'WorkArea', on_delete=models.SET_NULL, null=True)
    work_days = models.CharField(max_length=100, null=True, blank=True)
    # Status Field
    status = models.CharField(
        max_length=10, choices=STATUS_CHOICES, default='active')
    # Optional Fields
    ntsa_inspection_doc = models.FileField(
        upload_to='photos/ntsa/', null=True, blank=True)
    ntsa_expiry_date = models.DateField(null=True, blank=True)
    insurance_doc = models.FileField(
        upload_to='photos/insurance/', null=True, blank=True)
    insurance_expiry_date = models.DateField(null=True, blank=True)
    logbook = models.FileField(
        upload_to='photos/logbook/', null=True, blank=True)
    lease_agreement = models.FileField(
        upload_to='photos/lease/', null=True, blank=True)
    vehicle_photo = models.FileField(
        upload_to='photos/vehicles/', null=True, blank=True)
    year_of_manufacture = models.IntegerField(null=True, blank=True)
    # vehicle_expiry_date = models.DateField(
    #     blank=True, null=True,
    #     help_text='Enter vehicle registration expiry date')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.registration_number} - {self.vehicle_make} {self.vehicle_model}"


# notifications model
class Notification(models.Model):
    TYPE_CHOICES = [
        ('sms', 'SMS'),
        ('email', 'Email'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    ]

    id = models.AutoField(primary_key=True)
    subject = models.CharField(max_length=255, blank=True, null=True)
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    # Email address or phone number
    recipient = models.CharField(max_length=255)
    status = models.CharField(
        max_length=10, choices=STATUS_CHOICES, default='pending')
    # message content
    message = models.TextField(null=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.type} notification to {self.recipient} - {self.status}"

    def mark_as_sent(self):
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.save()

    def mark_as_failed(self):
        self.status = 'pending'
        self.save()


# subscribers model
class NotificationSubscriber(models.Model):
    user = models.OneToOneField(
        User, on_delete=models.CASCADE, primary_key=True)
    subscribed_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.email} - Subscribed"


# Expenditure Model
class Expenditure(models.Model):
    vehicle = models.ForeignKey(
        'Vehicle', related_name='expenditures', on_delete=models.CASCADE)
    date = models.DateField()
    item_name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)  # New field added
    quantity = models.PositiveIntegerField(default=1)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)
    partner = models.ForeignKey(
        'Partner', related_name='expenditures', on_delete=models.CASCADE)
    deleted = models.BooleanField(default=False)
    def __str__(self):
        return f"{self.item_name} - {self.vehicle.registration_number} - {self.date}"


# Added a revenue class to handle revenue for drivers
class Revenue(models.Model):
    PAYMENT_STATUS_CHOICES = [
        ('on_time', 'On Time'),
        ('late', 'Late'),
        ('missed', 'Missed'),
    ]

    driver = models.ForeignKey('Driver', on_delete=models.CASCADE, related_name='revenues', blank=True, null=True)
    vehicle = models.ForeignKey( 'Vehicle', on_delete=models.CASCADE, related_name='vehicle_revenues')
    date = models.DateField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    confirmation_message = models.CharField(max_length=255, blank=True, null=True, unique=True)
    status = models.CharField(max_length=10, choices=PAYMENT_STATUS_CHOICES, default='on_time')
    deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        driver_name = self.driver.first_name if self.driver else "Unknown Driver"
        vehicle_reg = self.vehicle.registration_number if self.vehicle else "Unknown Vehicle"
        return f"{driver_name} - {vehicle_reg} - {self.date}"

    def calculate_payment_status(self):
        """
        Calculate and set the payment status based on payment settings and creation time.
        Returns the calculated status.
        """
        from .rating_utils import get_payment_deadline
        
        # Get payment settings for the vehicle or partner-wide settings
        payment_settings = PaymentSettings.objects.filter(
            vehicle=self.vehicle
        ).first() or PaymentSettings.objects.filter(
            partner=self.vehicle.partner,
            vehicle__isnull=True
        ).first()
        
        if not payment_settings:
            # No payment settings, consider any payment as on-time
            self.status = 'on_time'
            return 'on_time'
        
        # Check if this is a payment day
        payment_day = self.date.strftime('%A')  # Monday, Tuesday, etc.
        if payment_day not in payment_settings.payment_days:
            # Not a payment day, mark as missed
            self.status = 'missed'
            return 'missed'
        
        # Get the deadline for this payment (combines date with deadline time)
        deadline = get_payment_deadline(self.vehicle, self.date)
        if not deadline:
            # No deadline found, consider as on-time
            self.status = 'on_time'
            return 'on_time'
        
        # Compare creation time with deadline (considering timezone)
        if self.created_at:
            # Convert to naive datetime for comparison
            created_naive = self.created_at.replace(tzinfo=None) if self.created_at.tzinfo else self.created_at
            deadline_naive = deadline.replace(tzinfo=None) if deadline.tzinfo else deadline
            
            if created_naive <= deadline_naive:
                self.status = 'on_time'
                return 'on_time'
            else:
                self.status = 'late'
                return 'late'
        else:
            # No creation time, assume on-time
            self.status = 'on_time'
            return 'on_time'

    def update_missed_to_late_for_date(self):
        """
        Update any existing missed revenue records for this date to 'late'.
        This is called when a payment is made for a specific date.
        """
        from .rating_utils import handle_payment_for_date
        
        if self.vehicle and self.vehicle.partner:
            updated_count = handle_payment_for_date(self.vehicle.partner, self.date, self.vehicle)
            return updated_count
        return 0

    def save(self, *args, **kwargs):
        """
        Override save method to automatically calculate and set payment status.
        """
        # Check if this is a new record or an update
        is_new = self.pk is None
        
        # Save the record first
        super().save(*args, **kwargs)
        
        # Only update payment status for actual payments (not missed payment records)
        # Missed payment records are created with status='missed' and should stay that way
        if not (hasattr(self, 'confirmation_message') and 
                self.confirmation_message and 
                'Missed payment for' in self.confirmation_message):
            # Update payment status based on timing vs. payment settings
            from .rating_utils import update_payment_status_for_revenue
            update_payment_status_for_revenue(self)
        
        # If this is a new payment and it's for a past date, update any missed records to late
        if is_new and self.date <= timezone.now().date():
            self.update_missed_to_late_for_date()

# Configuration of a partner PaymentSettings Model
class PaymentSettings(models.Model):
    DAYS_OF_WEEK = [
        ('Monday', 'Monday'),
        ('Tuesday', 'Tuesday'),
        ('Wednesday', 'Wednesday'),
        ('Thursday', 'Thursday'),
        ('Friday', 'Friday'),
        ('Saturday', 'Saturday'),
        ('Sunday', 'Sunday'),
    ]

    partner = models.ForeignKey(
        'Partner', on_delete=models.CASCADE, related_name='payment_settings')
    vehicle = models.ForeignKey('Vehicle', on_delete=models.CASCADE,
                                related_name='payment_settings', null=True, blank=True)
    daily_amount = models.DecimalField(max_digits=10, decimal_places=2)
    # e.g., "Monday,Wednesday,Friday"
    payment_days = models.CharField(max_length=100)
    deadline_time = models.TimeField()  # e.g., 17:00:00 for 5:00 PM
    

    def __str__(self):
        return f"Payment Settings for {self.partner} (Vehicle: {self.vehicle if self.vehicle else 'All'})"

#enquiry serailizer  
class Enquiry(models.Model):
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    phone_number = models.CharField(max_length=15)
    email = models.EmailField()
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.email}"


#Subscriptions Model 
class Subscription(models.Model):
    PLAN_TYPES = (
        ('Free', 'Free Trial'),
        ('Standard', 'Standard'),
        ('Pro', 'Pro'),
    )
    BILLING_CYCLES = (
        ('Monthly', 'Monthly'),
        ('Quarterly', 'Quarterly'),
        ('Yearly', 'Yearly'),
    )
    STATUS_CHOICES = (
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
    )

    partner = models.ForeignKey(Partner, on_delete=models.CASCADE)
    plan_type = models.CharField(max_length=10, choices=PLAN_TYPES)
    billing_cycle = models.CharField(max_length=10, choices=BILLING_CYCLES, null=True)
    vehicle_count = models.PositiveIntegerField(default=1)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateTimeField(auto_now_add=True)
    expiry_date = models.DateTimeField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if not self.pk:  # On creation
            if not self.start_date:
                self.start_date = timezone.now()

            # Convert 0 vehicle_count to 1 (for all plans)
            if self.vehicle_count == 0:
                self.vehicle_count = 1

            self.set_expiry_date()
            self.calculate_amount()
        super().save(*args, **kwargs)


    def set_expiry_date(self):
        if not self.start_date:
            raise ValidationError("start_date must be set before calculating expiry_date")
        if self.plan_type == 'Free':
            self.expiry_date = self.start_date + timedelta(days=14)
        elif self.billing_cycle == 'Monthly':
            self.expiry_date = self.start_date + timedelta(days=30)
        elif self.billing_cycle == 'Quarterly':
            self.expiry_date = self.start_date + timedelta(days=90)
        elif self.billing_cycle == 'Yearly':
            self.expiry_date = self.start_date + timedelta(days=365)

    def calculate_amount(self):
        pricing = {
            'Standard': {'Monthly': 1000, 'Quarterly': 2700, 'Yearly': 9600},
            'Pro': {'Monthly': 1800, 'Quarterly': 4860, 'Yearly': 17280}
        }
        if self.plan_type == 'Free':
            self.amount = 0
        else:
            if not self.billing_cycle:
                raise ValidationError("Billing cycle required for paid plans")
            self.amount = pricing[self.plan_type][self.billing_cycle] * self.vehicle_count

    @classmethod
    def expire_old_subscriptions(cls):
        """Call the database function to expire old subscriptions"""
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT expire_old_subscriptions();")
            result = cursor.fetchone()
            return result[0] if result else 0

    def __str__(self):
        return f"{self.partner} - {self.plan_type} ({self.status})"


# Driver Rating Model
class DriverRating(models.Model):
    driver = models.ForeignKey('Driver', on_delete=models.CASCADE, related_name='ratings')
    vehicle = models.ForeignKey('Vehicle', on_delete=models.CASCADE, related_name='driver_ratings')
    partner = models.ForeignKey('Partner', on_delete=models.CASCADE, related_name='driver_ratings')

    # Rating calculation fields
    rating_score = models.DecimalField(max_digits=4, decimal_places=2, default=0.00)
    total_points = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_payment_days = models.PositiveIntegerField(default=0)

    # Payment tracking fields
    last_payment_date = models.DateField(null=True, blank=True)
    consecutive_non_payment_days = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)  # False if stopped due to 3+ days non-payment

    # Metadata
    calculation_start_date = models.DateField()
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('driver', 'vehicle')
        ordering = ['-last_updated']

    def __str__(self):
        driver_name = f"{self.driver.first_name} {self.driver.last_name}" if self.driver else "Unknown Driver"
        vehicle_reg = self.vehicle.registration_number if self.vehicle else "Unknown Vehicle"
        return f"{driver_name} - {vehicle_reg} - Rating: {self.rating_score}"

    def calculate_rating(self):
        """Calculate the current rating score based on total points and payment days"""
        if self.total_payment_days > 0:
            self.rating_score = self.total_points / self.total_payment_days
        else:
            self.rating_score = 0.00
        return self.rating_score


# #Payment Transaction
class PaymentTransaction(models.Model):
    TRANSACTION_TYPES = (
        ('subscription', 'Subscription'),
        ('job_posting', 'Job Posting'),
        ('job_application', 'Job Application'),
    )
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('success', 'Success'),
        ('failed', 'Failed'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, null=True, blank=True)
    phone_number = models.CharField(max_length=15)
    transaction_id = models.CharField(max_length=50, unique=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    reference_id = models.PositiveIntegerField()
    mpesa_receipt_number = models.CharField(max_length=50, null=True, blank=True)
    transaction_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.transaction_type} - {self.transaction_id} ({self.status})"
    
#Vehicle subscription
class SubscriptionVehicle(models.Model):
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE)
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('subscription', 'vehicle')

    def save(self, *args, **kwargs):
        # Validate vehicle count
        subscription = self.subscription
        current_count = SubscriptionVehicle.objects.filter(subscription=subscription).count()
        if current_count >= subscription.vehicle_count:
            raise ValidationError("Vehicle count exceeds subscription limit.")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.vehicle} in {self.subscription}"