from datetime import datetime, timedelta, time
from django.utils import timezone
from django.db.models import Q
from .models import PaymentSettings, Revenue, DriverRating, Vehicle


def get_payment_deadline(vehicle, payment_date):
    """
    Get the payment deadline for a specific vehicle and date.
    Returns the deadline datetime or None if no payment settings found.
    """
    try:
        # Get payment settings for the vehicle or partner
        payment_settings = PaymentSettings.objects.filter(
            Q(vehicle=vehicle) | Q(partner=vehicle.partner, vehicle__isnull=True)
        ).first()
        
        if not payment_settings:
            return None
        
        # Check if the payment_date is a payment day
        payment_days = [day.strip() for day in payment_settings.payment_days.split(',')]
        day_name = payment_date.strftime('%A')
        
        if day_name not in payment_days:
            return None
        
        # Combine payment date with deadline time
        deadline_datetime = timezone.make_aware(
            datetime.combine(payment_date, payment_settings.deadline_time)
        )
        
        return deadline_datetime
        
    except Exception as e:
        print(f"Error getting payment deadline: {str(e)}")
        return None


def calculate_payment_status(revenue_record):
    """
    Get payment status and points from a revenue record.
    Returns tuple: (status, points)
    Status: 'on_time', 'late', 'missed'
    Points: 5.0 for on_time, 2.5 for late, 0.0 for missed
    """
    if not revenue_record:
        return 'missed', 0.0
    
    # Use the stored status from the Revenue model
    status = revenue_record.status
    
    if status == 'on_time':
        return 'on_time', 5.0
    elif status == 'late':
        return 'late', 2.5
    elif status == 'missed':
        return 'missed', 0.0
    else:
        # Fallback to on_time if status is not set
        return 'on_time', 5.0


def update_payment_status_for_revenue(revenue_record):
    """
    Update payment status for a revenue record based on payment timing vs. payment settings.
    This should be called when a payment is made or when checking payment status.
    Only updates status for actual payments, not missed payment records.
    
    Args:
        revenue_record: Revenue instance to update
    """
    if not revenue_record:
        return
    
    # Don't update status for missed payment records
    if (hasattr(revenue_record, 'confirmation_message') and 
        revenue_record.confirmation_message and 
        'Missed payment for' in revenue_record.confirmation_message):
        return revenue_record.status
    
    # Get payment settings for this vehicle
    vehicle = revenue_record.vehicle
    payment_settings = PaymentSettings.objects.filter(
        vehicle=vehicle
    ).first() or PaymentSettings.objects.filter(
        partner=vehicle.partner,
        vehicle__isnull=True
    ).first()
    
    if not payment_settings:
        return
    
    # Get the payment deadline for this date
    deadline = get_payment_deadline(vehicle, revenue_record.date)
    
    if not deadline:
        return  # No payment expected for this date
    
    # Check if payment was made on time
    payment_time = revenue_record.created_at  # Use the actual payment time
    
    if payment_time <= deadline:
        # Payment made before deadline
        if revenue_record.status != 'on_time':
            revenue_record.status = 'on_time'
            revenue_record.save()
    else:
        # Payment made after deadline
        if revenue_record.status not in ['late', 'missed']:
            revenue_record.status = 'late'
            revenue_record.save()
    
    return revenue_record.status


def get_expected_payment_days(vehicle, start_date, end_date):
    """
    Get list of dates when payments are expected for a vehicle within a date range.
    """
    try:
        payment_settings = PaymentSettings.objects.filter(
            Q(vehicle=vehicle) | Q(partner=vehicle.partner, vehicle__isnull=True)
        ).first()

        if not payment_settings:
            return []

        payment_days = [day.strip() for day in payment_settings.payment_days.split(',')]
        day_mapping = {
            'Monday': 0, 'Tuesday': 1, 'Wednesday': 2, 'Thursday': 3,
            'Friday': 4, 'Saturday': 5, 'Sunday': 6
        }

        payment_day_numbers = [day_mapping.get(day) for day in payment_days if day in day_mapping]

        expected_dates = []
        current_date = start_date

        while current_date <= end_date:
            if current_date.weekday() in payment_day_numbers:
                expected_dates.append(current_date)
            current_date += timedelta(days=1)

        return expected_dates

    except Exception as e:
        print(f"Error getting expected payment days: {str(e)}")
        return []


def calculate_driver_rating(driver, vehicle, calculation_date=None, start_date=None):
    """
    Calculate or update driver rating for a specific driver-vehicle combination.
    Returns the DriverRating instance.

    Args:
        driver: Driver instance
        vehicle: Vehicle instance
        calculation_date: End date for calculation (defaults to today)
        start_date: Start date for calculation (defaults to earliest revenue or 30 days ago)
    """
    if calculation_date is None:
        calculation_date = timezone.now().date()

    # Determine start date if not provided
    if start_date is None:
        # Try to find the earliest revenue record for this combination
        earliest_revenue = Revenue.objects.filter(
            driver=driver,
            vehicle=vehicle,
            deleted=False
        ).order_by('date').first()

        if earliest_revenue:
            start_date = earliest_revenue.date
        else:
            # Default to 30 days ago if no revenue found
            start_date = calculation_date - timedelta(days=30)

    # Create missed revenue records for expected payment days
    create_missed_revenue_records(vehicle.partner, start_date, calculation_date)

    # Get or create driver rating record
    driver_rating, created = DriverRating.objects.get_or_create(
        driver=driver,
        vehicle=vehicle,
        defaults={
            'partner': vehicle.partner,
            'calculation_start_date': start_date
        }
    )

    # Update calculation start date if this is an existing record and we have a better start date
    if not created and start_date < driver_rating.calculation_start_date:
        driver_rating.calculation_start_date = start_date
        driver_rating.save()
    
    # If rating is inactive due to 3+ days non-payment, check if we should reactivate
    if not driver_rating.is_active:
        # Check if there's a recent payment
        recent_payment = Revenue.objects.filter(
            driver=driver,
            vehicle=vehicle,
            date__gte=calculation_date - timedelta(days=3),
            deleted=False
        ).exists()
        
        if recent_payment:
            driver_rating.is_active = True
            driver_rating.consecutive_non_payment_days = 0
    
    # If still inactive, return without calculation
    if not driver_rating.is_active:
        return driver_rating
    
    # Get all revenue records for this driver-vehicle combination from start date
    revenues = Revenue.objects.filter(
        driver=driver,
        vehicle=vehicle,
        date__gte=driver_rating.calculation_start_date,
        date__lte=calculation_date,
        deleted=False
    ).order_by('date')
    
    # Get expected payment days from start date to calculation date
    expected_dates = get_expected_payment_days(
        vehicle,
        driver_rating.calculation_start_date,
        calculation_date
    )

    total_points = 0.0
    total_payment_days = len(expected_dates)
    last_payment_date = None
    consecutive_non_payment_days = 0
    
    # Create a mapping of payment dates to revenue records
    payment_map = {revenue.date: revenue for revenue in revenues}
    
    # Calculate points for each expected payment day using stored status
    for expected_date in expected_dates:
        if expected_date in payment_map:
            # Payment was made - use stored status
            revenue = payment_map[expected_date]
            status, points = calculate_payment_status(revenue)
            total_points += points
            last_payment_date = expected_date
            consecutive_non_payment_days = 0
        else:
            # No payment made - this should not happen now since we create missed records
            total_points += 0.0
            consecutive_non_payment_days += 1
    
    # Check if we should stop calculating due to 3+ consecutive non-payment days
    if consecutive_non_payment_days >= 3:
        driver_rating.is_active = False
    else:
        driver_rating.is_active = True
    
    # Update rating record
    driver_rating.total_points = total_points
    driver_rating.total_payment_days = total_payment_days
    driver_rating.consecutive_non_payment_days = consecutive_non_payment_days
    driver_rating.last_payment_date = last_payment_date
    driver_rating.calculate_rating()  # This will update rating_score
    driver_rating.save()
    
    return driver_rating


def get_driver_age_group(driver):
    """
    Get driver age group based on date of birth.
    Returns: 'below_25', '26_39', '40_above', or 'unknown'
    """
    if not driver.date_of_birth:
        return 'unknown'
    
    today = timezone.now().date()
    age = today.year - driver.date_of_birth.year
    
    # Adjust for birthday not yet occurred this year
    if today < driver.date_of_birth.replace(year=today.year):
        age -= 1
    
    if age < 25:
        return 'below_25'
    elif 25 <= age <= 39:
        return '26_39'
    else:
        return '40_above'








def create_or_update_driver_rating(driver, vehicle, partner):
    """
    Create or update driver rating for a specific driver-vehicle combination.
    This is a wrapper function that calls calculate_driver_rating with proper parameters.
    
    Args:
        driver: Driver instance
        vehicle: Vehicle instance
        partner: Partner instance
    
    Returns:
        DriverRating instance
    """
    return calculate_driver_rating(driver, vehicle)


def create_missed_revenue_records(partner, start_date=None, end_date=None):
    """
    Create missed revenue records for expected payment days when no payment was made.
    Only creates missed payments for active drivers and days that have passed.
    Stops creating missed revenue after 3 consecutive days of non-payment.
    
    Args:
        partner: Partner instance
        start_date: Start date for checking (defaults to 30 days ago)
        end_date: End date for checking (defaults to today)
    """
    from django.utils import timezone
    from datetime import timedelta
    
    if end_date is None:
        end_date = timezone.now().date()
    if start_date is None:
        start_date = end_date - timedelta(days=30)
    
    # Only process days that have passed (yesterday and earlier)
    yesterday = timezone.now().date() - timedelta(days=1)
    end_date = min(end_date, yesterday)
    
    if start_date > end_date:
        return 0  # No days to process
    
    # Get all vehicles for the partner with active drivers only
    vehicles = Vehicle.objects.filter(
        partner=partner,
        driver__isnull=False,  # Only vehicles with drivers
        status='active'
    ).select_related('driver')
    
    created_count = 0
    
    for vehicle in vehicles:
        # Skip if driver is not active (you may need to add an active field to Driver model)
        if not vehicle.driver:
            continue
            
        # Get payment settings for this vehicle or partner-wide
        payment_settings = PaymentSettings.objects.filter(
            vehicle=vehicle
        ).first() or PaymentSettings.objects.filter(
            partner=partner,
            vehicle__isnull=True
        ).first()
        
        if not payment_settings:
            continue
        
        # Get expected payment days in the date range
        expected_dates = get_expected_payment_days(vehicle, start_date, end_date)
        
        # Track consecutive missed days
        consecutive_missed_days = 0
        
        for expected_date in expected_dates:
            # Check if a revenue record already exists for this date and vehicle
            existing_revenue = Revenue.objects.filter(
                vehicle=vehicle,
                date=expected_date,
                deleted=False
            ).first()
            
            if not existing_revenue:
                consecutive_missed_days += 1
                
                # Double-check to prevent race conditions
                if not Revenue.objects.filter(
                    vehicle=vehicle,
                    date=expected_date,
                    deleted=False
                ).exists():
                    
                    # Stop creating missed revenue after 3 consecutive days
                    if consecutive_missed_days <= 3:
                        # Create missed revenue record with unique message
                        import uuid
                        unique_id = str(uuid.uuid4())[:8]
                        unique_message = f"Missed payment for {expected_date.strftime('%Y-%m-%d')} - {vehicle.registration_number} - {unique_id}"
                        missed_revenue = Revenue.objects.create(
                            driver=vehicle.driver,
                            vehicle=vehicle,
                            date=expected_date,
                            amount=payment_settings.daily_amount,
                            status='missed',  # Set status directly to missed
                            confirmation_message=unique_message
                        )
                        created_count += 1
                    # After 3 consecutive days, stop creating missed revenue
                    # This accounts for drivers not working or vehicles in maintenance
            else:
                # Reset consecutive missed days if a payment was made
                consecutive_missed_days = 0
    
    return created_count


def update_missed_to_late_revenue_records(partner, payment_date):
    """
    Update missed revenue records to 'late' when a payment is made for that date.
    
    Args:
        partner: Partner instance
        payment_date: Date for which payment was made
    """
    # Find missed revenue records for this date and partner's vehicles
    missed_revenues = Revenue.objects.filter(
        vehicle__partner=partner,
        date=payment_date,
        status='missed',
        deleted=False
    )
    
    updated_count = 0
    
    for missed_revenue in missed_revenues:
        # Update status to late
        missed_revenue.status = 'late'
        missed_revenue.save()
        updated_count += 1
    
    return updated_count


def handle_payment_for_date(partner, payment_date, vehicle=None):
    """
    Handle when a payment is made for a specific date.
    Updates any existing missed revenue records for that date to 'late'.
    
    Args:
        partner: Partner instance
        payment_date: Date for which payment was made
        vehicle: Specific vehicle (optional, if None, handles all partner vehicles)
    """
    if vehicle:
        # Handle specific vehicle
        missed_revenues = Revenue.objects.filter(
            vehicle=vehicle,
            date=payment_date,
            status='missed',
            deleted=False
        )
    else:
        # Handle all partner vehicles
        missed_revenues = Revenue.objects.filter(
            vehicle__partner=partner,
            date=payment_date,
            status='missed',
            deleted=False
        )
    
    updated_count = 0
    
    for missed_revenue in missed_revenues:
        # Update status to late
        missed_revenue.status = 'late'
        missed_revenue.save()
        updated_count += 1
    
    return updated_count
