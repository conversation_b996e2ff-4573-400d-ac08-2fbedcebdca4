from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import DailyPaymentReportView, FinancialReportView, JobApplicationViewSet, DriverViewSet, PartnerViewSet, JobViewSet, ProfitReportView, UpdatePasswordView, VehicleMakeViewSet, VehicleModelViewSet, WorkAreaViewSet, VehicleTypeViewSet, VerifyEmail, MyUserDetailView, TestEmailView, PasswordResetRequestView, PasswordResetConfirmView, PartnerDriverViewSet, VehicleViewSet, NotificationPreferenceView, ExpenditureViewSet, RevenueViewSet, PaymentSettingsViewSet, EnquiryView, STKPushView, PaymentCallbackView,SubscriptionActivateView, AccessTokenView,STKPushQueryView, SubscriptionViewSet, DashboardSummaryView, MonthlyProfitTrendsView
from django.conf.urls.static import static
from django.conf import settings

router = DefaultRouter()
router.register(r'drivers', DriverViewSet, basename='driver')
router.register(r'partner-drivers', PartnerDriverViewSet,
                basename='partner-drivers')
router.register(r'partners', PartnerViewSet, basename='partner')
router.register(r'jobs', JobViewSet, basename='job')
router.register(r'vehicle-makes', VehicleMakeViewSet, basename='vehicle-make')
router.register(r'vehicle-models', VehicleModelViewSet,
                basename='vehicle-model')
router.register(r'work-areas', WorkAreaViewSet, basename='work-area')
router.register(r'vehicle-types', VehicleTypeViewSet, basename='vehicle-type')
router.register(r'applications', JobApplicationViewSet, basename='application')
router.register(r'vehicles', VehicleViewSet, basename='vehicle')
router.register(r'expenditures', ExpenditureViewSet, basename='expenditure')
router.register(r'revenues', RevenueViewSet, basename='revenue')
router.register(r'payment-settings', PaymentSettingsViewSet, basename='payment-settings')
router.register(r'subscriptions', SubscriptionViewSet, basename='subscription')


urlpatterns = [
    path('', include(router.urls)),
    path('verify-email/<uuid:verification_code>/',
         VerifyEmail.as_view(), name='verify-email'),
    path('users/me/', MyUserDetailView.as_view(), name='my-user-detail'),
    path('update-password', UpdatePasswordView.as_view(), name='update-password'),
    path('test-email/', TestEmailView.as_view(), name='test-email'),
    path('applications/<int:pk>/remove_by_partner/',
         JobApplicationViewSet.as_view({'post': 'remove_by_partner'})),
    path('applications/<int:pk>/withdraw_by_driver/',
         JobApplicationViewSet.as_view({'post': 'withdraw_by_driver'})),
    path('password-reset/', PasswordResetRequestView.as_view(),
         name='password-reset-request'),
    path('password-reset-confirm/', PasswordResetConfirmView.as_view(),
         name='password-reset-confirm'),
    path('notifications/preferences/', NotificationPreferenceView.as_view(),
         name='notification-preferences'),
    path('financial-report/', FinancialReportView.as_view(), 
         name='financial_report'), 
    path('profit-report/', ProfitReportView.as_view(),
         name='profit_report'),
    path('daily-payment-report/', DailyPaymentReportView.as_view(), 
         name='daily_payment_report'),
    path('enquiry/', EnquiryView.as_view(),
         name='enquiry'),

    path('access-token/', AccessTokenView.as_view(), name='access-token'),
    path('stk-push/', STKPushView.as_view(), name='stk-push'),
    path('callback/', PaymentCallbackView.as_view(), name='payment_callback'),
    path('subscriptions/activate/', SubscriptionActivateView.as_view(), name='subscription-activate'),
    path('stk-push-query/', STKPushQueryView.as_view(), name='stk-push-query'),

    # Optimized Dashboard Endpoints
    path('dashboard/summary/', DashboardSummaryView.as_view(), name='dashboard-summary'),
    path('dashboard/monthly-profit-trends/', MonthlyProfitTrendsView.as_view(), name='monthly-profit-trends'),

]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
