from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.mail import send_mail
from django.conf import settings
from .models import User, NotificationSubscriber, Job, Notification, Revenue
import threading

# Signal for NotificationSubscriber management


@receiver(post_save, sender=User)
def manage_notification_subscriber(sender, instance, created, **kwargs):
    if instance.notification_subscribe:
        NotificationSubscriber.objects.get_or_create(user=instance)
    else:
        NotificationSubscriber.objects.filter(user=instance).delete()


def _send_job_notification_emails(job_id):
    """
    Internal function to send HTML-formatted job notification emails
    """
    try:
        job = Job.objects.get(id=job_id)
        
        # Get the vehicle model name - handle both string and object reference
        if hasattr(job.vehicle_model, 'name'):
            # If vehicle_model is an object with a name attribute
            vehicle_model = job.vehicle_model.name
        else:
            # If vehicle_model is already a string
            vehicle_model = str(job.vehicle_model)
        
        # Clean up vehicle_model to prevent duplicate words
        # Split by spaces and remove duplicates while preserving order
        model_parts = []
        for part in vehicle_model.split():
            if not model_parts or part.lower() != model_parts[-1].lower():
                model_parts.append(part)
        cleaned_vehicle_model = " ".join(model_parts)
        
        subscribed_users = User.objects.filter(
            notificationsubscriber__isnull=False,
            is_active=True,
            email_verified=True,
            role='driver',
            driver__partner__isnull=True,  # Not hired (no partner assigned)
            driver__work_area=job.preferred_work_area  # Match location
        ).select_related('driver')

        # Use the cleaned vehicle model in the subject
        subject = f"New Job Alert: {cleaned_vehicle_model}"
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #f8f9fa; padding: 20px; text-align: center; border-radius: 5px; }}
                .job-details {{ 
                    background-color: #f8f9fa; 
                    padding: 20px; 
                    margin: 20px 0; 
                    border-radius: 5px;
                    border-left: 4px solid #007bff;
                }}
                .button {{ 
                    background-color: #007bff; 
                    color: white !important; 
                    padding: 12px 25px; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    display: inline-block;
                    font-weight: bold;
                }}
                .note {{ 
                    font-style: italic; 
                    color: #666666; 
                    text-align: center;
                    margin: 20px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h2>🚗 New Job Opportunity!</h2>
                </div>
                
                <p>Hello {{{{first_name}}}},</p>
                <p>An exciting job opportunity matching your preferences is now available!</p>
                
                <div class="job-details">
                    <h3 style="margin-top: 0;">Job Details:</h3>
                    <p><strong>🚘 Vehicle:</strong> {cleaned_vehicle_model}</p>
                    <p><strong>📍 Work Area:</strong> {job.preferred_work_area}</p>
                    <p><strong>📅 Work Days:</strong> {job.work_days}</p>
                    <p><strong>📋 Requirements:</strong></p>
                    <p>{job.requirements}</p>
                </div>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="{settings.FRONTEND_URL}/jobs/{job.id}" class="button">
                        View & Apply Now
                    </a>
                </div>

                <p class="note">
                    Jobs are assigned on a first-come, first-served basis. Apply early!
                </p>

                <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
                
                <p style="text-align: center; color: #666666;">
                    Best regards,<br>
                    <strong>KadereConnect Team</strong>
                </p>
            </div>
        </body>
        </html>
        """

        for user in subscribed_users:
            try:
                html_message = html_template.replace(
                    '{{first_name}}', user.first_name or 'Driver')

                # Create notification record
                notification = Notification.objects.create(
                    subject=subject,
                    type="email",
                    recipient=user.email,
                    status="pending",
                    message=html_message
                )

                # Send HTML email
                send_mail(
                    subject=subject,
                    message='',  # Empty plain text message
                    html_message=html_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    fail_silently=False
                )

                notification.mark_as_sent()

            except Exception as e:
                if 'notification' in locals():
                    notification.mark_as_failed()
                print(f"Error sending to {user.email}: {str(e)}")

    except Exception as e:
        print(f"Error processing job notifications: {str(e)}")


@receiver(post_save, sender=Job)
def handle_new_job(sender, instance, created, **kwargs):
    """
    Signal handler for new job posts
    """
    if created:
        # Launch email sending in background thread
        email_thread = threading.Thread(
            target=_send_job_notification_emails,
            args=(instance.id,),
            daemon=True  # Daemon thread won't block program exit
        )
        email_thread.start()


@receiver(post_save, sender=Revenue)
def update_driver_rating_on_payment(sender, instance, created, **kwargs):
    """
    Signal handler to automatically update driver ratings when a revenue record is created or updated.
    This ensures ratings are calculated automatically when drivers make payments.
    """
    if instance.driver and instance.vehicle and not instance.deleted:
        try:
            # Import here to avoid circular imports
            from .rating_utils import calculate_driver_rating

            # Calculate rating in a background thread to avoid blocking the request
            def update_rating():
                try:
                    calculate_driver_rating(instance.driver, instance.vehicle, instance.date)
                except Exception as e:
                    print(f"Error updating driver rating for driver {instance.driver.id} and vehicle {instance.vehicle.id}: {str(e)}")

            # Run in background thread
            rating_thread = threading.Thread(
                target=update_rating,
                daemon=True
            )
            rating_thread.start()

        except Exception as e:
            print(f"Error in driver rating signal: {str(e)}")