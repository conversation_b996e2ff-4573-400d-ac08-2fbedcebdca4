"""
Comprehensive Test Suite for Driver Rating System

This test suite covers all aspects of the driver rating functionality:
1. Rating calculation from fresh data
2. Management commands testing
3. API endpoints testing
4. Edge cases and error handling
5. Permission and authentication testing
6. Data integrity and consistency testing

Test Structure:
- TestDriverRatingCalculation: Core rating calculation logic
- TestDriverRatingManagementCommands: Management command functionality
- TestDriverRatingAPIEndpoints: All API endpoints
- TestDriverRatingPermissions: Authentication and authorization
- TestDriverRatingEdgeCases: Edge cases and error scenarios
- TestDriverRatingIntegration: End-to-end integration tests
"""

from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.management import call_command
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from decimal import Decimal
from datetime import datetime, timedelta, time
from io import StringIO
import json

from .models import (
    Partner, Driver, Vehicle, Revenue, PaymentSettings,
    DriverRating, VehicleMake, VehicleModel, WorkArea
)
from .rating_utils import (
    calculate_driver_rating, create_missed_revenue_records,
    get_expected_payment_days, calculate_payment_status,
    get_driver_age_group, create_or_update_driver_rating
)

User = get_user_model()


def mock_handle_driver_partner_transfer(driver, old_partner, new_partner):
    """Mock function for driver partner transfer testing"""
    # Update driver's partner
    driver.partner = new_partner
    driver.save()

    # Update all ratings to new partner
    ratings_updated = DriverRating.objects.filter(driver=driver).update(partner=new_partner)

    return {
        'success': True,
        'ratings_updated': ratings_updated,
        'message': f'Driver transferred from {old_partner.first_name} to {new_partner.first_name}'
    }


class TestDriverRatingCalculation(TestCase):
    """Test core driver rating calculation functionality"""

    def setUp(self):
        """Set up test data for rating calculations"""
        # Create test users
        self.partner_user = User.objects.create_user(
            username='testpartner',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.driver_user = User.objects.create_user(
            username='testdriver',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        # Create partner
        self.partner = Partner.objects.create(
            user=self.partner_user,
            first_name='Test',
            last_name='Partner',
            email='<EMAIL>',
            mobile_number='1234567890'
        )

        # Create driver
        self.driver = Driver.objects.create(
            user=self.driver_user,
            first_name='Test',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime(1990, 1, 1).date(),
            mobile_number='0987654321'
        )

        # Create vehicle
        self.vehicle = Vehicle.objects.create(
            partner=self.partner,
            driver=self.driver,
            registration_number='TEST123',
            status='active'
        )

        # Create payment settings
        self.payment_settings = PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('1000.00'),
            payment_days='Monday,Wednesday,Friday',
            deadline_time=time(17, 0)  # 5:00 PM
        )

        # Base date for testing (Monday)
        self.base_date = datetime(2024, 1, 1).date()

    def test_fresh_rating_calculation_no_payments(self):
        """Test rating calculation from fresh data with no payments"""
        # Calculate rating with no revenue records
        rating = calculate_driver_rating(self.driver, self.vehicle, self.base_date + timedelta(days=7))

        self.assertIsNotNone(rating)
        self.assertEqual(rating.driver, self.driver)
        self.assertEqual(rating.vehicle, self.vehicle)
        self.assertEqual(rating.partner, self.partner)
        self.assertEqual(rating.rating_score, Decimal('0.00'))
        self.assertEqual(rating.total_points, Decimal('0.00'))
        self.assertTrue(rating.total_payment_days >= 0)  # Should have expected payment days
        self.assertFalse(rating.is_active)  # Should be inactive due to no payments

    def test_fresh_rating_calculation_perfect_payments(self):
        """Test rating calculation with all on-time payments"""
        # Create on-time payments for expected days
        payment_dates = [
            self.base_date,  # Monday
            self.base_date + timedelta(days=2),  # Wednesday
            self.base_date + timedelta(days=4),  # Friday
        ]

        for payment_date in payment_dates:
            Revenue.objects.create(
                driver=self.driver,
                vehicle=self.vehicle,
                date=payment_date,
                amount=Decimal('1000.00'),
                status='on_time',
                created_at=timezone.make_aware(
                    datetime.combine(payment_date, time(16, 0))  # Before deadline
                )
            )

        # Calculate rating
        rating = calculate_driver_rating(
            self.driver,
            self.vehicle,
            self.base_date + timedelta(days=6)
        )

        self.assertEqual(rating.rating_score, Decimal('5.00'))  # Perfect score
        self.assertEqual(rating.total_points, Decimal('15.00'))  # 3 payments × 5 points
        self.assertEqual(rating.total_payment_days, 3)
        self.assertTrue(rating.is_active)
        self.assertEqual(rating.consecutive_non_payment_days, 0)

    def test_fresh_rating_calculation_mixed_payments(self):
        """Test rating calculation with mixed payment statuses"""
        # Create mixed payment records
        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=self.base_date,  # Monday - on time
            amount=Decimal('1000.00'),
            status='on_time',
            created_at=timezone.make_aware(
                datetime.combine(self.base_date, time(16, 0))
            )
        )

        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=self.base_date + timedelta(days=2),  # Wednesday - late
            amount=Decimal('1000.00'),
            status='late',
            created_at=timezone.make_aware(
                datetime.combine(self.base_date + timedelta(days=2), time(18, 0))
            )
        )

        # Friday - missed (no revenue record will be created by missed revenue creation)

        # Calculate rating
        rating = calculate_driver_rating(
            self.driver,
            self.vehicle,
            self.base_date + timedelta(days=6)
        )

        # Expected: 5.0 (on-time) + 2.5 (late) + 0.0 (missed) = 7.5 points over 3 days = 2.5 average
        expected_score = Decimal('2.50')
        self.assertEqual(rating.rating_score, expected_score)
        self.assertEqual(rating.total_points, Decimal('7.50'))
        self.assertEqual(rating.total_payment_days, 3)
        self.assertTrue(rating.is_active)  # Still active as not 3+ consecutive missed days

    def test_consecutive_non_payment_deactivation(self):
        """Test that rating becomes inactive after 3+ consecutive non-payment days"""
        # Create payment settings for daily payments to test consecutive days
        PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('500.00'),
            payment_days='Monday,Tuesday,Wednesday,Thursday,Friday',
            deadline_time=time(17, 0)
        )

        # Create missed revenue records for 4 consecutive days
        for i in range(4):
            Revenue.objects.create(
                driver=self.driver,
                vehicle=self.vehicle,
                date=self.base_date + timedelta(days=i),
                amount=Decimal('0.00'),
                status='missed'
            )

        # Calculate rating
        rating = calculate_driver_rating(
            self.driver,
            self.vehicle,
            self.base_date + timedelta(days=5)
        )

        self.assertFalse(rating.is_active)  # Should be inactive
        self.assertTrue(rating.consecutive_non_payment_days >= 3)
        self.assertEqual(rating.rating_score, Decimal('0.00'))

    def test_rating_reactivation_after_payment(self):
        """Test that rating reactivates when payment is made after being inactive"""
        # First make rating inactive
        for i in range(4):
            Revenue.objects.create(
                driver=self.driver,
                vehicle=self.vehicle,
                date=self.base_date + timedelta(days=i),
                amount=Decimal('0.00'),
                status='missed'
            )

        rating = calculate_driver_rating(
            self.driver,
            self.vehicle,
            self.base_date + timedelta(days=5)
        )
        self.assertFalse(rating.is_active)

        # Now make a payment
        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=self.base_date + timedelta(days=5),
            amount=Decimal('1000.00'),
            status='on_time'
        )

        # Recalculate rating
        rating = calculate_driver_rating(
            self.driver,
            self.vehicle,
            self.base_date + timedelta(days=6)
        )

        self.assertTrue(rating.is_active)  # Should be reactivated
        self.assertEqual(rating.consecutive_non_payment_days, 0)

    def test_payment_status_calculation(self):
        """Test payment status and points calculation"""
        # Test on-time payment
        on_time_revenue = Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=self.base_date,
            amount=Decimal('1000.00'),
            status='on_time'
        )
        status, points = calculate_payment_status(on_time_revenue)
        self.assertEqual(status, 'on_time')
        self.assertEqual(points, 5.0)

        # Test late payment
        late_revenue = Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=self.base_date + timedelta(days=1),
            amount=Decimal('1000.00'),
            status='late'
        )
        status, points = calculate_payment_status(late_revenue)
        self.assertEqual(status, 'late')
        self.assertEqual(points, 2.5)

        # Test missed payment
        missed_revenue = Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=self.base_date + timedelta(days=2),
            amount=Decimal('0.00'),
            status='missed'
        )
        status, points = calculate_payment_status(missed_revenue)
        self.assertEqual(status, 'missed')
        self.assertEqual(points, 0.0)

        # Test None revenue
        status, points = calculate_payment_status(None)
        self.assertEqual(status, 'missed')
        self.assertEqual(points, 0.0)

    def test_driver_age_group_calculation(self):
        """Test driver age group calculation"""
        # Test young driver (under 25)
        young_driver = Driver.objects.create(
            user=User.objects.create_user(
                username='youngdriver',
                email='<EMAIL>',
                password='testpass123',
                role='driver'
            ),
            first_name='Young',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime.now().date() - timedelta(days=20*365)  # 20 years old
        )
        age_group = get_driver_age_group(young_driver)
        self.assertEqual(age_group, 'Under 25')

        # Test middle-aged driver (25-40)
        middle_driver = Driver.objects.create(
            user=User.objects.create_user(
                username='middledriver',
                email='<EMAIL>',
                password='testpass123',
                role='driver'
            ),
            first_name='Middle',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime.now().date() - timedelta(days=30*365)  # 30 years old
        )
        age_group = get_driver_age_group(middle_driver)
        self.assertEqual(age_group, '25-40')

        # Test older driver (over 40)
        older_driver = Driver.objects.create(
            user=User.objects.create_user(
                username='olderdriver',
                email='<EMAIL>',
                password='testpass123',
                role='driver'
            ),
            first_name='Older',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime.now().date() - timedelta(days=45*365)  # 45 years old
        )
        age_group = get_driver_age_group(older_driver)
        self.assertEqual(age_group, 'Over 40')

    def test_expected_payment_days_calculation(self):
        """Test calculation of expected payment days"""
        start_date = self.base_date  # Monday
        end_date = self.base_date + timedelta(days=6)  # Sunday

        expected_dates = get_expected_payment_days(self.vehicle, start_date, end_date)

        # Should include Monday, Wednesday, Friday
        expected = {
            self.base_date,  # Monday
            self.base_date + timedelta(days=2),  # Wednesday
            self.base_date + timedelta(days=4),  # Friday
        }

        self.assertEqual(set(expected_dates), expected)

    def test_create_or_update_driver_rating(self):
        """Test the create_or_update_driver_rating utility function"""
        # First call should create a new rating
        rating1 = create_or_update_driver_rating(self.driver, self.vehicle, self.partner)
        self.assertIsNotNone(rating1)
        self.assertEqual(rating1.driver, self.driver)
        self.assertEqual(rating1.vehicle, self.vehicle)
        self.assertEqual(rating1.partner, self.partner)

        # Second call should update the existing rating
        rating2 = create_or_update_driver_rating(self.driver, self.vehicle, self.partner)
        self.assertEqual(rating1.id, rating2.id)  # Same instance

        # Verify only one rating exists
        rating_count = DriverRating.objects.filter(
            driver=self.driver,
            vehicle=self.vehicle
        ).count()
        self.assertEqual(rating_count, 1)


class TestDriverRatingManagementCommands(TestCase):
    """Test management command functionality for driver ratings"""

    def setUp(self):
        """Set up test data for management command tests"""
        # Create test users
        self.partner_user = User.objects.create_user(
            username='cmdpartner',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.driver_user = User.objects.create_user(
            username='cmddriver',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        # Create partner
        self.partner = Partner.objects.create(
            user=self.partner_user,
            first_name='Command',
            last_name='Partner',
            email='<EMAIL>',
            mobile_number='1234567890'
        )

        # Create driver
        self.driver = Driver.objects.create(
            user=self.driver_user,
            first_name='Command',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime(1990, 1, 1).date(),
            mobile_number='0987654321'
        )

        # Create vehicle
        self.vehicle = Vehicle.objects.create(
            partner=self.partner,
            driver=self.driver,
            registration_number='CMD123',
            status='active'
        )

        # Create payment settings
        self.payment_settings = PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('1000.00'),
            payment_days='Monday,Wednesday,Friday',
            deadline_time=time(17, 0)
        )

    def test_management_command_dry_run(self):
        """Test management command in dry-run mode"""
        out = StringIO()

        # Test dry run - should not make any changes
        call_command(
            'manage_driver_ratings',
            '--action', 'check_missed_payments',
            '--dry-run',
            stdout=out
        )

        output = out.getvalue()
        self.assertIn('DRY RUN', output)

        # Verify no revenue records were created
        revenue_count = Revenue.objects.filter(vehicle=self.vehicle).count()
        self.assertEqual(revenue_count, 0)

    def test_management_command_create_missed_payments(self):
        """Test management command for creating missed payment records"""
        out = StringIO()

        # Run command to create missed payments
        call_command(
            'manage_driver_ratings',
            '--action', 'check_missed_payments',
            '--partner-id', str(self.partner.id),
            '--days', '7',
            stdout=out
        )

        output = out.getvalue()
        self.assertIn('Processing partner', output)

        # Verify missed revenue records were created
        missed_revenues = Revenue.objects.filter(
            vehicle=self.vehicle,
            status='missed'
        )
        self.assertTrue(missed_revenues.exists())

    def test_management_command_calculate_ratings(self):
        """Test management command for calculating ratings"""
        # First create some revenue data
        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=timezone.now().date(),
            amount=Decimal('1000.00'),
            status='on_time'
        )

        out = StringIO()

        # Run command to calculate ratings
        call_command(
            'manage_driver_ratings',
            '--action', 'calculate_ratings',
            '--partner-id', str(self.partner.id),
            stdout=out
        )

        output = out.getvalue()
        self.assertIn('Calculating ratings', output)

        # Verify rating was created
        rating = DriverRating.objects.filter(
            driver=self.driver,
            vehicle=self.vehicle
        ).first()
        self.assertIsNotNone(rating)
        self.assertGreater(rating.rating_score, 0)

    def test_management_command_full_sync(self):
        """Test management command for full synchronization"""
        out = StringIO()

        # Run full sync command
        call_command(
            'manage_driver_ratings',
            '--action', 'full_sync',
            '--partner-id', str(self.partner.id),
            '--days', '30',
            stdout=out
        )

        output = out.getvalue()
        self.assertIn('Full synchronization', output)

        # Verify both missed payments and ratings were processed
        missed_revenues = Revenue.objects.filter(
            vehicle=self.vehicle,
            status='missed'
        )
        ratings = DriverRating.objects.filter(driver=self.driver)

        self.assertTrue(missed_revenues.exists() or ratings.exists())

    def test_management_command_invalid_action(self):
        """Test management command with invalid action"""
        out = StringIO()
        err = StringIO()

        # This should raise a CommandError or handle gracefully
        try:
            call_command(
                'manage_driver_ratings',
                '--action', 'invalid_action',
                stdout=out,
                stderr=err
            )
        except Exception as e:
            # Command should handle invalid actions gracefully
            self.assertIn('invalid', str(e).lower())

    def test_management_command_specific_date_range(self):
        """Test management command with specific date range"""
        out = StringIO()

        start_date = (timezone.now().date() - timedelta(days=14)).strftime('%Y-%m-%d')
        end_date = (timezone.now().date() - timedelta(days=7)).strftime('%Y-%m-%d')

        call_command(
            'manage_driver_ratings',
            '--action', 'check_missed_payments',
            '--partner-id', str(self.partner.id),
            '--start-date', start_date,
            '--end-date', end_date,
            stdout=out
        )

        output = out.getvalue()
        self.assertIn('Processing partner', output)

        # Verify missed payments were created for the specified date range
        missed_revenues = Revenue.objects.filter(
            vehicle=self.vehicle,
            status='missed',
            date__gte=start_date,
            date__lte=end_date
        )
        # Should have some missed payments for the date range
        self.assertTrue(missed_revenues.exists())


class TestDriverRatingAPIEndpoints(APITestCase):
    """Test all API endpoints related to driver ratings"""

    def setUp(self):
        """Set up test data for API endpoint tests"""
        # Create test users
        self.partner_user = User.objects.create_user(
            username='apipartner',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.driver_user = User.objects.create_user(
            username='apidriver',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        self.admin_user = User.objects.create_user(
            username='apiadmin',
            email='<EMAIL>',
            password='testpass123',
            role='admin',
            is_staff=True
        )

        # Create partner
        self.partner = Partner.objects.create(
            user=self.partner_user,
            first_name='API',
            last_name='Partner',
            email='<EMAIL>',
            mobile_number='1234567890'
        )

        # Create driver
        self.driver = Driver.objects.create(
            user=self.driver_user,
            first_name='API',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime(1990, 1, 1).date(),
            mobile_number='0987654321'
        )

        # Create vehicle
        self.vehicle = Vehicle.objects.create(
            partner=self.partner,
            driver=self.driver,
            registration_number='API123',
            status='active'
        )

        # Create payment settings
        self.payment_settings = PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('1000.00'),
            payment_days='Monday,Wednesday,Friday',
            deadline_time=time(17, 0)
        )

        # Create some test revenue data
        self.revenue1 = Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=timezone.now().date(),
            amount=Decimal('1000.00'),
            status='on_time'
        )

        self.revenue2 = Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=timezone.now().date() - timedelta(days=1),
            amount=Decimal('1000.00'),
            status='late'
        )

        # Create initial rating
        self.rating = create_or_update_driver_rating(self.driver, self.vehicle, self.partner)

        # Set up API client
        self.client = APIClient()

    def test_driver_rating_endpoint_as_driver(self):
        """Test driver rating endpoint when accessed by the driver themselves"""
        self.client.force_authenticate(user=self.driver_user)

        url = f'/api/drivers/{self.driver.id}/rating/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Verify response structure
        self.assertIn('driver_id', data)
        self.assertIn('driver_name', data)
        self.assertIn('driver_code', data)
        self.assertIn('age_group', data)
        self.assertIn('current_rating', data)
        self.assertIn('rating_level', data)

        self.assertEqual(data['driver_id'], self.driver.id)
        self.assertEqual(data['driver_name'], f"{self.driver.first_name} {self.driver.last_name}")

    def test_driver_rating_endpoint_as_partner(self):
        """Test driver rating endpoint when accessed by partner"""
        self.client.force_authenticate(user=self.partner_user)

        url = f'/api/drivers/{self.driver.id}/rating/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Partner should be able to see their driver's rating
        self.assertIn('current_rating', data)
        self.assertIn('total_points', data)
        self.assertIn('payment_completion_rate', data)

    def test_driver_rating_endpoint_unauthorized_driver(self):
        """Test driver rating endpoint when accessed by different driver"""
        # Create another driver
        other_driver_user = User.objects.create_user(
            username='otherdriver',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        self.client.force_authenticate(user=other_driver_user)

        url = f'/api/drivers/{self.driver.id}/rating/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_driver_rating_trend_endpoint(self):
        """Test driver rating trend endpoint"""
        self.client.force_authenticate(user=self.driver_user)

        url = f'/api/drivers/{self.driver.id}/rating_trend/'

        # Test weekly trend
        response = self.client.get(url, {'period': 'weekly'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn('trend_data', data)
        self.assertIn('period', data)
        self.assertEqual(data['period'], 'weekly')

        # Test monthly trend
        response = self.client.get(url, {'period': 'monthly'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['period'], 'monthly')

    def test_calculate_rating_endpoint_as_driver(self):
        """Test manual rating calculation endpoint as driver"""
        self.client.force_authenticate(user=self.driver_user)

        url = f'/api/drivers/{self.driver.id}/calculate_rating/'
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn('message', data)
        self.assertIn('results', data)
        self.assertTrue(len(data['results']) > 0)

    def test_calculate_rating_endpoint_with_vehicle_filter(self):
        """Test manual rating calculation with specific vehicle"""
        self.client.force_authenticate(user=self.driver_user)

        url = f'/api/drivers/{self.driver.id}/calculate_rating/'
        response = self.client.post(url, {'vehicle_id': self.vehicle.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['vehicle_id'], self.vehicle.id)

    def test_partner_recalculate_all_ratings_endpoint(self):
        """Test partner endpoint for recalculating all driver ratings"""
        self.client.force_authenticate(user=self.partner_user)

        url = '/api/partner-drivers/recalculate_all_driver_ratings/'
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn('message', data)
        self.assertIn('results', data)
        self.assertIn('total_drivers_processed', data)

    def test_partner_recalculate_ratings_with_date_filter(self):
        """Test partner rating recalculation with date filter"""
        self.client.force_authenticate(user=self.partner_user)

        url = '/api/partner-drivers/recalculate_all_driver_ratings/'
        recalculate_date = (timezone.now().date() - timedelta(days=30)).strftime('%Y-%m-%d')

        response = self.client.post(url, {
            'recalculate_from_date': recalculate_date
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertIn('recalculate_from_date', data)
        self.assertEqual(data['recalculate_from_date'], recalculate_date)

    def test_partner_recalculate_ratings_with_vehicle_filter(self):
        """Test partner rating recalculation for specific vehicle"""
        self.client.force_authenticate(user=self.partner_user)

        url = '/api/partner-drivers/recalculate_all_driver_ratings/'
        response = self.client.post(url, {
            'vehicle_id': self.vehicle.id
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        # Should only process one vehicle
        self.assertTrue(len(data['results']) >= 1)
        for result in data['results']:
            self.assertEqual(result['vehicle_id'], self.vehicle.id)

    def test_unauthenticated_access_to_rating_endpoints(self):
        """Test that unauthenticated users cannot access rating endpoints"""
        # Don't authenticate

        url = f'/api/drivers/{self.driver.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        url = f'/api/drivers/{self.driver.id}/rating_trend/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

        url = f'/api/drivers/{self.driver.id}/calculate_rating/'
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_rating_endpoint_with_no_ratings(self):
        """Test rating endpoint when driver has no ratings yet"""
        # Create a new driver with no ratings
        new_driver_user = User.objects.create_user(
            username='newdriver',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        new_driver = Driver.objects.create(
            user=new_driver_user,
            first_name='New',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime(1995, 1, 1).date()
        )

        self.client.force_authenticate(user=new_driver_user)

        url = f'/api/drivers/{new_driver.id}/rating/'
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()

        self.assertEqual(data['current_rating'], 0.0)
        self.assertEqual(data['rating_level'], 'No Rating')
        self.assertIn('No ratings available yet', data['message'])

    def test_rating_endpoint_error_handling(self):
        """Test rating endpoint error handling"""
        self.client.force_authenticate(user=self.driver_user)

        # Test with non-existent driver ID
        url = '/api/drivers/99999/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_calculate_rating_with_invalid_vehicle(self):
        """Test rating calculation with invalid vehicle ID"""
        self.client.force_authenticate(user=self.driver_user)

        url = f'/api/drivers/{self.driver.id}/calculate_rating/'
        response = self.client.post(url, {'vehicle_id': 99999})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        data = response.json()
        self.assertIn('Vehicle not found', data['detail'])


class TestDriverRatingPermissions(APITestCase):
    """Test authentication and authorization for driver rating endpoints"""

    def setUp(self):
        """Set up test data for permission tests"""
        # Create multiple partners and drivers
        self.partner1_user = User.objects.create_user(
            username='partner1',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.partner2_user = User.objects.create_user(
            username='partner2',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.driver1_user = User.objects.create_user(
            username='driver1',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        self.driver2_user = User.objects.create_user(
            username='driver2',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        # Create partners
        self.partner1 = Partner.objects.create(
            user=self.partner1_user,
            first_name='Partner',
            last_name='One',
            email='<EMAIL>'
        )

        self.partner2 = Partner.objects.create(
            user=self.partner2_user,
            first_name='Partner',
            last_name='Two',
            email='<EMAIL>'
        )

        # Create drivers under different partners
        self.driver1 = Driver.objects.create(
            user=self.driver1_user,
            first_name='Driver',
            last_name='One',
            email='<EMAIL>',
            partner=self.partner1,
            date_of_birth=datetime(1990, 1, 1).date()
        )

        self.driver2 = Driver.objects.create(
            user=self.driver2_user,
            first_name='Driver',
            last_name='Two',
            email='<EMAIL>',
            partner=self.partner2,
            date_of_birth=datetime(1990, 1, 1).date()
        )

        self.client = APIClient()

    def test_driver_can_only_view_own_rating(self):
        """Test that drivers can only view their own ratings"""
        self.client.force_authenticate(user=self.driver1_user)

        # Should be able to view own rating
        url = f'/api/drivers/{self.driver1.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should NOT be able to view other driver's rating
        url = f'/api/drivers/{self.driver2.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_partner_can_view_own_drivers_ratings(self):
        """Test that partners can view their own drivers' ratings"""
        self.client.force_authenticate(user=self.partner1_user)

        # Should be able to view own driver's rating
        url = f'/api/drivers/{self.driver1.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Should be able to view other drivers' ratings (temporary for testing)
        url = f'/api/drivers/{self.driver2.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_partner_recalculate_ratings_permission(self):
        """Test that only partners can recalculate ratings"""
        # Partner should be able to recalculate
        self.client.force_authenticate(user=self.partner1_user)
        url = '/api/partner-drivers/recalculate_all_driver_ratings/'
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Driver should NOT be able to recalculate all ratings
        self.client.force_authenticate(user=self.driver1_user)
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestDriverRatingEdgeCases(TestCase):
    """Test edge cases and error scenarios for driver ratings"""

    def setUp(self):
        """Set up test data for edge case tests"""
        self.partner_user = User.objects.create_user(
            username='edgepartner',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.driver_user = User.objects.create_user(
            username='edgedriver',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        self.partner = Partner.objects.create(
            user=self.partner_user,
            first_name='Edge',
            last_name='Partner',
            email='<EMAIL>'
        )

        self.driver = Driver.objects.create(
            user=self.driver_user,
            first_name='Edge',
            last_name='Driver',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=datetime(1990, 1, 1).date()
        )

        self.vehicle = Vehicle.objects.create(
            partner=self.partner,
            driver=self.driver,
            registration_number='EDGE123',
            status='active'
        )

    def test_rating_calculation_with_no_payment_settings(self):
        """Test rating calculation when no payment settings exist"""
        # No payment settings created
        rating = calculate_driver_rating(self.driver, self.vehicle)

        # Should still create a rating record but with zero values
        self.assertIsNotNone(rating)
        self.assertEqual(rating.rating_score, Decimal('0.00'))
        self.assertEqual(rating.total_payment_days, 0)

    def test_rating_calculation_with_future_dates(self):
        """Test rating calculation with future calculation date"""
        future_date = timezone.now().date() + timedelta(days=30)

        PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('1000.00'),
            payment_days='Monday,Wednesday,Friday',
            deadline_time=time(17, 0)
        )

        rating = calculate_driver_rating(self.driver, self.vehicle, future_date)

        # Should handle future dates gracefully
        self.assertIsNotNone(rating)
        self.assertTrue(rating.total_payment_days >= 0)

    def test_rating_with_driver_without_birth_date(self):
        """Test rating calculation for driver without birth date"""
        driver_no_birth = Driver.objects.create(
            user=User.objects.create_user(
                username='nobirth',
                email='<EMAIL>',
                password='testpass123',
                role='driver'
            ),
            first_name='No',
            last_name='Birth',
            email='<EMAIL>',
            partner=self.partner,
            date_of_birth=None  # No birth date
        )

        age_group = get_driver_age_group(driver_no_birth)
        self.assertEqual(age_group, 'Unknown')

    def test_rating_with_very_large_amounts(self):
        """Test rating calculation with very large payment amounts"""
        PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('999999.99'),
            payment_days='Monday',
            deadline_time=time(17, 0)
        )

        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=timezone.now().date(),
            amount=Decimal('999999.99'),
            status='on_time'
        )

        rating = calculate_driver_rating(self.driver, self.vehicle)

        # Should handle large amounts without overflow
        self.assertIsNotNone(rating)
        self.assertEqual(rating.rating_score, Decimal('5.00'))

    def test_rating_with_zero_amount_payments(self):
        """Test rating calculation with zero amount payments"""
        PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('0.00'),
            payment_days='Monday',
            deadline_time=time(17, 0)
        )

        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=timezone.now().date(),
            amount=Decimal('0.00'),
            status='missed'
        )

        rating = calculate_driver_rating(self.driver, self.vehicle)

        # Should handle zero amounts
        self.assertIsNotNone(rating)
        self.assertEqual(rating.rating_score, Decimal('0.00'))

    def test_partner_transfer_functionality(self):
        """Test driver partner transfer functionality"""
        # Create second partner
        partner2_user = User.objects.create_user(
            username='partner2',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        partner2 = Partner.objects.create(
            user=partner2_user,
            first_name='Partner',
            last_name='Two',
            email='<EMAIL>'
        )

        # Create initial rating
        PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('1000.00'),
            payment_days='Monday',
            deadline_time=time(17, 0)
        )

        Revenue.objects.create(
            driver=self.driver,
            vehicle=self.vehicle,
            date=timezone.now().date(),
            amount=Decimal('1000.00'),
            status='on_time'
        )

        initial_rating = calculate_driver_rating(self.driver, self.vehicle)
        initial_points = initial_rating.total_points

        # Transfer driver to new partner
        result = mock_handle_driver_partner_transfer(self.driver, self.partner, partner2)

        # Verify transfer was successful
        self.assertTrue(result['success'])
        self.assertEqual(result['ratings_updated'], 1)

        # Verify rating continues with new partner
        updated_rating = DriverRating.objects.get(
            driver=self.driver,
            vehicle=self.vehicle
        )
        self.assertEqual(updated_rating.partner, partner2)
        self.assertEqual(updated_rating.total_points, initial_points)  # Points preserved
        self.assertTrue(updated_rating.is_active)  # Remains active

    def test_create_missed_revenue_records_edge_cases(self):
        """Test missed revenue record creation edge cases"""
        PaymentSettings.objects.create(
            partner=self.partner,
            vehicle=self.vehicle,
            daily_amount=Decimal('1000.00'),
            payment_days='Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday',  # Daily
            deadline_time=time(17, 0)
        )

        # Test with very short date range
        start_date = timezone.now().date()
        end_date = start_date

        created_count = create_missed_revenue_records(self.partner, start_date, end_date)

        # Should handle single day range
        self.assertTrue(created_count >= 0)

        # Test with very long date range
        start_date = timezone.now().date() - timedelta(days=365)
        end_date = timezone.now().date()

        created_count = create_missed_revenue_records(self.partner, start_date, end_date)

        # Should handle long ranges without issues
        self.assertTrue(created_count >= 0)


class TestDriverRatingIntegration(APITestCase):
    """Integration tests for the complete driver rating system"""

    def setUp(self):
        """Set up comprehensive test data for integration tests"""
        # Create multiple partners, drivers, and vehicles
        self.setup_test_data()
        self.client = APIClient()

    def setup_test_data(self):
        """Create comprehensive test data"""
        # Partner 1 with 2 drivers and 3 vehicles
        self.partner1_user = User.objects.create_user(
            username='intpartner1',
            email='<EMAIL>',
            password='testpass123',
            role='partner'
        )

        self.partner1 = Partner.objects.create(
            user=self.partner1_user,
            first_name='Integration',
            last_name='Partner1',
            email='<EMAIL>'
        )

        # Create drivers
        self.driver1_user = User.objects.create_user(
            username='intdriver1',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        self.driver1 = Driver.objects.create(
            user=self.driver1_user,
            first_name='Integration',
            last_name='Driver1',
            email='<EMAIL>',
            partner=self.partner1,
            date_of_birth=datetime(1985, 1, 1).date()
        )

        self.driver2_user = User.objects.create_user(
            username='intdriver2',
            email='<EMAIL>',
            password='testpass123',
            role='driver'
        )

        self.driver2 = Driver.objects.create(
            user=self.driver2_user,
            first_name='Integration',
            last_name='Driver2',
            email='<EMAIL>',
            partner=self.partner1,
            date_of_birth=datetime(1995, 1, 1).date()
        )

        # Create vehicles
        self.vehicle1 = Vehicle.objects.create(
            partner=self.partner1,
            driver=self.driver1,
            registration_number='INT001',
            status='active'
        )

        self.vehicle2 = Vehicle.objects.create(
            partner=self.partner1,
            driver=self.driver1,
            registration_number='INT002',
            status='active'
        )

        self.vehicle3 = Vehicle.objects.create(
            partner=self.partner1,
            driver=self.driver2,
            registration_number='INT003',
            status='active'
        )

        # Create payment settings
        for vehicle in [self.vehicle1, self.vehicle2, self.vehicle3]:
            PaymentSettings.objects.create(
                partner=self.partner1,
                vehicle=vehicle,
                daily_amount=Decimal('1000.00'),
                payment_days='Monday,Wednesday,Friday',
                deadline_time=time(17, 0)
            )

    def test_end_to_end_rating_workflow(self):
        """Test complete end-to-end rating workflow"""
        # Step 1: Create revenue records with mixed statuses
        base_date = timezone.now().date() - timedelta(days=7)  # Start a week ago

        # Driver 1, Vehicle 1: Perfect payments
        for i in [0, 2, 4]:  # Monday, Wednesday, Friday
            Revenue.objects.create(
                driver=self.driver1,
                vehicle=self.vehicle1,
                date=base_date + timedelta(days=i),
                amount=Decimal('1000.00'),
                status='on_time'
            )

        # Driver 1, Vehicle 2: Mixed payments
        Revenue.objects.create(
            driver=self.driver1,
            vehicle=self.vehicle2,
            date=base_date,  # Monday - on time
            amount=Decimal('1000.00'),
            status='on_time'
        )
        Revenue.objects.create(
            driver=self.driver1,
            vehicle=self.vehicle2,
            date=base_date + timedelta(days=2),  # Wednesday - late
            amount=Decimal('1000.00'),
            status='late'
        )
        # Friday - missed (no record)

        # Driver 2, Vehicle 3: Poor payments
        Revenue.objects.create(
            driver=self.driver2,
            vehicle=self.vehicle3,
            date=base_date,  # Monday - late
            amount=Decimal('1000.00'),
            status='late'
        )
        # Wednesday and Friday - missed

        # Step 2: Run management command to create missed payments
        call_command(
            'manage_driver_ratings',
            '--action', 'check_missed_payments',
            '--partner-id', str(self.partner1.id),
            '--days', '7'
        )

        # Step 3: Calculate ratings
        call_command(
            'manage_driver_ratings',
            '--action', 'calculate_ratings',
            '--partner-id', str(self.partner1.id)
        )

        # Step 4: Verify ratings through API
        self.client.force_authenticate(user=self.partner1_user)

        # Check Driver 1 ratings
        url = f'/api/drivers/{self.driver1.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        driver1_data = response.json()

        # Driver 1 should have good ratings (perfect + mixed)
        self.assertGreater(driver1_data['current_rating'], 3.0)

        # Check Driver 2 ratings
        url = f'/api/drivers/{self.driver2.id}/rating/'
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        driver2_data = response.json()

        # Driver 2 should have lower ratings (poor payments)
        self.assertLess(driver2_data['current_rating'], 2.0)

        # Step 5: Test rating trends
        url = f'/api/drivers/{self.driver1.id}/rating_trend/'
        response = self.client.get(url, {'period': 'weekly'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        trend_data = response.json()
        self.assertIn('trend_data', trend_data)

        # Step 6: Test manual recalculation
        url = '/api/partner-drivers/recalculate_all_driver_ratings/'
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        recalc_data = response.json()
        self.assertGreater(recalc_data['total_drivers_processed'], 0)

    def test_rating_system_with_filtering(self):
        """Test rating system with various filtering options"""
        # Create diverse test data
        self.create_diverse_revenue_data()

        # Calculate all ratings
        call_command(
            'manage_driver_ratings',
            '--action', 'full_sync',
            '--partner-id', str(self.partner1.id),
            '--days', '30'
        )

        self.client.force_authenticate(user=self.partner1_user)

        # Test filtering by date range
        url = f'/api/drivers/{self.driver1.id}/rating_trend/'
        response = self.client.get(url, {
            'period': 'monthly',
            'start_date': (timezone.now().date() - timedelta(days=30)).strftime('%Y-%m-%d'),
            'end_date': timezone.now().date().strftime('%Y-%m-%d')
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test vehicle-specific rating calculation
        url = f'/api/drivers/{self.driver1.id}/calculate_rating/'
        response = self.client.post(url, {'vehicle_id': self.vehicle1.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['vehicle_id'], self.vehicle1.id)

    def create_diverse_revenue_data(self):
        """Create diverse revenue data for testing"""
        base_date = timezone.now().date() - timedelta(days=30)

        # Create 30 days of varied payment data
        for day in range(30):
            current_date = base_date + timedelta(days=day)

            # Skip weekends for this test
            if current_date.weekday() >= 5:
                continue

            # Create payments with different patterns for each driver/vehicle
            if day % 3 == 0:  # Every 3rd day
                # Driver 1, Vehicle 1 - mostly on time
                status = 'on_time' if day % 6 != 0 else 'late'
                Revenue.objects.create(
                    driver=self.driver1,
                    vehicle=self.vehicle1,
                    date=current_date,
                    amount=Decimal('1000.00'),
                    status=status
                )

                # Driver 2, Vehicle 3 - mixed performance
                status = 'late' if day % 4 == 0 else ('on_time' if day % 2 == 0 else 'missed')
                if status != 'missed':
                    Revenue.objects.create(
                        driver=self.driver2,
                        vehicle=self.vehicle3,
                        date=current_date,
                        amount=Decimal('1000.00') if status != 'missed' else Decimal('0.00'),
                        status=status
                    )

    def test_concurrent_rating_calculations(self):
        """Test that concurrent rating calculations don't cause issues"""
        # Create some revenue data
        Revenue.objects.create(
            driver=self.driver1,
            vehicle=self.vehicle1,
            date=timezone.now().date(),
            amount=Decimal('1000.00'),
            status='on_time'
        )

        # Simulate concurrent calculations
        rating1 = calculate_driver_rating(self.driver1, self.vehicle1)
        rating2 = calculate_driver_rating(self.driver1, self.vehicle1)

        # Should return the same rating instance
        self.assertEqual(rating1.id, rating2.id)
        self.assertEqual(rating1.rating_score, rating2.rating_score)

        # Verify only one rating exists
        rating_count = DriverRating.objects.filter(
            driver=self.driver1,
            vehicle=self.vehicle1
        ).count()
        self.assertEqual(rating_count, 1)

    def test_data_consistency_after_operations(self):
        """Test that data remains consistent after various operations"""
        # Create initial data
        Revenue.objects.create(
            driver=self.driver1,
            vehicle=self.vehicle1,
            date=timezone.now().date(),
            amount=Decimal('1000.00'),
            status='on_time'
        )

        # Calculate initial rating
        initial_rating = calculate_driver_rating(self.driver1, self.vehicle1)
        initial_score = initial_rating.rating_score

        # Add more revenue
        Revenue.objects.create(
            driver=self.driver1,
            vehicle=self.vehicle1,
            date=timezone.now().date() - timedelta(days=1),
            amount=Decimal('1000.00'),
            status='late'
        )

        # Recalculate
        updated_rating = calculate_driver_rating(self.driver1, self.vehicle1)

        # Score should change appropriately
        self.assertNotEqual(initial_score, updated_rating.rating_score)

        # Verify data integrity
        self.assertEqual(updated_rating.driver, self.driver1)
        self.assertEqual(updated_rating.vehicle, self.vehicle1)
        self.assertEqual(updated_rating.partner, self.partner1)
        self.assertGreater(updated_rating.total_payment_days, 0)
        self.assertGreaterEqual(updated_rating.total_points, 0)
