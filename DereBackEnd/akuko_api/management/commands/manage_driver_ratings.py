from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta
from akuko_api.models import Driver, Vehicle, Revenue, PaymentSettings, DriverRating, Partner
from akuko_api.rating_utils import (
    calculate_driver_rating, 
    create_missed_revenue_records, 
    get_expected_payment_days,
    get_driver_age_group
)

import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Comprehensive driver rating and payment management command'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=[
                'create_missed_payments',    # Create missed payment records
                'calculate_ratings',         # Calculate/recalculate driver ratings
                'full_sync'                 # Complete sync: missed payments + ratings
            ],
            required=True,
            help='Action to perform'
        )
        parser.add_argument(
            '--partner-id',
            type=int,
            help='Specific partner ID to process (optional)'
        )
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date for processing (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date for processing (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making changes (for testing)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force processing even if not needed'
        )

    def handle(self, *args, **options):
        action = options['action']
        partner_id = options['partner_id']
        start_date = options['start_date']
        end_date = options['end_date']
        dry_run = options['dry_run']
        force = options['force']

        # Parse dates
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        if not end_date:
            end_date = timezone.now().date()
        if not start_date:
            # If no start date specified, find the earliest revenue record to process entire dataset
            from akuko_api.models import Revenue
            earliest_revenue = Revenue.objects.filter(deleted=False).order_by('date').first()
            if earliest_revenue:
                start_date = earliest_revenue.date
                self.stdout.write(f"No start date specified. Processing entire dataset from {start_date}")
            else:
                # Fallback to 30 days if no revenue records exist
                start_date = end_date - timedelta(days=30)
                self.stdout.write("No revenue records found. Using 30-day default range")

        self.stdout.write(f"Starting {action} for period {start_date} to {end_date}")
        if dry_run:
            self.stdout.write("DRY RUN MODE - No changes will be made")

        if action == 'create_missed_payments':
            self.create_missed_payments(partner_id, start_date, end_date, dry_run)
        elif action == 'calculate_ratings':
            self.calculate_ratings(partner_id, start_date, end_date, dry_run)
        elif action == 'full_sync':
            self.full_sync(partner_id, start_date, end_date, dry_run)

    def create_missed_payments(self, partner_id, start_date, end_date, dry_run):
        """Create missed payment records for expected payment days"""
        self.stdout.write("Creating missed payment records...")

        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_created = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")

            if not dry_run:
                # Close any existing database connections to prevent pool exhaustion
                from django.db import connection
                connection.close()

                created_count = create_missed_revenue_records(partner, start_date, end_date)
                total_created += created_count
                self.stdout.write(f"  Created {created_count} missed payment records")
            else:
                self.stdout.write(f"  Would create missed payment records for {start_date} to {end_date}")

        self.stdout.write(
            self.style.SUCCESS(f"Total missed payment records created: {total_created}")
        )

    def calculate_ratings(self, partner_id, start_date, end_date, dry_run):
        """Calculate or update driver ratings"""
        self.stdout.write("Calculating driver ratings...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_processed = 0
        total_updated = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")

            # Get all active driver-vehicle combinations for this partner
            vehicles_with_drivers = Vehicle.objects.filter(
                partner=partner,
                driver__isnull=False,
                status='active'
            ).select_related('driver')

            for vehicle in vehicles_with_drivers:
                try:
                    if not dry_run:
                        # Close any existing database connections to prevent pool exhaustion
                        from django.db import connection
                        connection.close()

                        rating = calculate_driver_rating(vehicle.driver, vehicle, end_date, start_date)
                        total_updated += 1
                    else:
                        # Check if rating exists
                        existing_rating = DriverRating.objects.filter(
                            driver=vehicle.driver,
                            vehicle=vehicle
                        ).first()
                        if existing_rating:
                            self.stdout.write(f"  Would update rating for {vehicle.driver.first_name} - {vehicle.registration_number}")
                        else:
                            self.stdout.write(f"  Would create rating for {vehicle.driver.first_name} - {vehicle.registration_number}")

                    total_processed += 1

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error processing {vehicle.driver.first_name} - {vehicle.registration_number}: {str(e)}")
                    )

        self.stdout.write(
            self.style.SUCCESS(f"Processed {total_processed} driver-vehicle combinations, updated {total_updated}")
        )

    def full_sync(self, partner_id, start_date, end_date, dry_run):
        """Complete synchronization: create missed payments and calculate ratings with optimized database usage"""
        self.stdout.write("Starting full synchronization...")

        # Step 1: Create missed payments
        self.stdout.write("Step 1: Creating missed payment records...")
        self.create_missed_payments(partner_id, start_date, end_date, dry_run)

        # Close database connections between steps
        from django.db import connection
        connection.close()

        # Step 2: Calculate ratings with batch processing
        self.stdout.write("Step 2: Calculating driver ratings...")
        self.calculate_ratings_optimized(partner_id, start_date, end_date, dry_run)

        self.stdout.write(
            self.style.SUCCESS("Full synchronization completed successfully!")
        )

    def calculate_ratings_optimized(self, partner_id, start_date, end_date, dry_run):
        """Ultra-optimized rating calculation with aggressive connection management"""
        self.stdout.write("Calculating driver ratings (ultra-optimized)...")

        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_processed = 0
        total_updated = 0
        batch_size = 1  # Process ONE vehicle at a time to minimize connections

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")

            # Get all active driver-vehicle combinations for this partner
            vehicles_with_drivers = Vehicle.objects.filter(
                partner=partner,
                driver__isnull=False,
                status='active'
            ).select_related('driver')

            # Process ONE vehicle at a time
            for vehicle in vehicles_with_drivers:
                try:
                    if not dry_run:
                        # Force close all database connections before each calculation
                        from django.db import connections
                        for conn in connections.all():
                            conn.close()

                        # Import here to avoid circular imports
                        from akuko_api.rating_utils import calculate_driver_rating
                        rating = calculate_driver_rating(vehicle.driver, vehicle, end_date, start_date)
                        total_updated += 1

                        # Force close connections again after calculation
                        for conn in connections.all():
                            conn.close()

                        self.stdout.write(f"  ✓ Updated rating for {vehicle.driver.first_name} - {vehicle.registration_number}")
                    else:
                        # Check if rating exists
                        existing_rating = DriverRating.objects.filter(
                            driver=vehicle.driver,
                            vehicle=vehicle
                        ).first()
                        if existing_rating:
                            self.stdout.write(f"  Would update rating for {vehicle.driver.first_name} - {vehicle.registration_number}")
                        else:
                            self.stdout.write(f"  Would create rating for {vehicle.driver.first_name} - {vehicle.registration_number}")

                    total_processed += 1

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error processing {vehicle.driver.first_name} - {vehicle.registration_number}: {str(e)}")
                    )
                    # Close connections even on error
                    if not dry_run:
                        from django.db import connections
                        for conn in connections.all():
                            conn.close()

        self.stdout.write(
            self.style.SUCCESS(f"Processed {total_processed} driver-vehicle combinations, updated {total_updated}")
        )
