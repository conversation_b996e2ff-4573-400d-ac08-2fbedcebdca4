from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import datetime, timedelta
from akuko_api.models import Driver, Vehicle, Revenue, PaymentSettings, DriverRating, Partner
from akuko_api.rating_utils import (
    calculate_driver_rating, 
    create_missed_revenue_records, 
    get_expected_payment_days,
    get_driver_age_group
)

import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Comprehensive driver rating and payment management command'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=[
                'calculate_ratings',
                'create_missed_revenue',
                'update_payment_status',
                'cleanup_inactive_ratings',
                'recalculate_all',
                'check_missed_payments'
            ],
            required=True,
            help='Action to perform'
        )
        parser.add_argument(
            '--partner-id',
            type=int,
            help='Specific partner ID to process (optional)'
        )
        parser.add_argument(
            '--start-date',
            type=str,
            help='Start date for processing (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--end-date',
            type=str,
            help='End date for processing (YYYY-MM-DD)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making changes (for testing)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force processing even if not needed'
        )

    def handle(self, *args, **options):
        action = options['action']
        partner_id = options['partner_id']
        start_date = options['start_date']
        end_date = options['end_date']
        dry_run = options['dry_run']
        force = options['force']

        # Parse dates
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        if not end_date:
            end_date = timezone.now().date()
        if not start_date:
            start_date = end_date - timedelta(days=30)

        self.stdout.write(f"Starting {action} for period {start_date} to {end_date}")
        if dry_run:
            self.stdout.write("DRY RUN MODE - No changes will be made")

        if action == 'calculate_ratings':
            self.calculate_ratings(partner_id, start_date, end_date, dry_run)
        elif action == 'create_missed_revenue':
            self.create_missed_revenue(partner_id, start_date, end_date, dry_run)
        elif action == 'update_payment_status':
            self.update_payment_status(partner_id, start_date, end_date, dry_run)
        elif action == 'cleanup_inactive_ratings':
            self.cleanup_inactive_ratings(partner_id, dry_run)
        elif action == 'recalculate_all':
            self.recalculate_all(partner_id, start_date, end_date, dry_run)
        elif action == 'check_missed_payments':
            self.check_missed_payments(partner_id, start_date, end_date, dry_run)

    def calculate_ratings(self, partner_id, start_date, end_date, dry_run):
        """Calculate or update driver ratings"""
        self.stdout.write("Calculating driver ratings...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_processed = 0
        total_updated = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")
            
            # Get all active driver-vehicle combinations for this partner
            vehicles_with_drivers = Vehicle.objects.filter(
                partner=partner,
                driver__isnull=False,
                status='active'
            ).select_related('driver')

            for vehicle in vehicles_with_drivers:
                try:
                    if not dry_run:
                        rating = calculate_driver_rating(vehicle.driver, vehicle, end_date, start_date)
                        total_updated += 1
                    else:
                        # Check if rating exists
                        existing_rating = DriverRating.objects.filter(
                            driver=vehicle.driver,
                            vehicle=vehicle
                        ).first()
                        if existing_rating:
                            self.stdout.write(f"  Would update rating for {vehicle.driver.first_name} - {vehicle.registration_number}")
                        else:
                            self.stdout.write(f"  Would create rating for {vehicle.driver.first_name} - {vehicle.registration_number}")
                    
                    total_processed += 1
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error processing {vehicle.driver.first_name} - {vehicle.registration_number}: {str(e)}")
                    )

        self.stdout.write(
            self.style.SUCCESS(f"Processed {total_processed} driver-vehicle combinations, updated {total_updated}")
        )

    def create_missed_revenue(self, partner_id, start_date, end_date, dry_run):
        """Create missed revenue records for expected payment days"""
        self.stdout.write("Creating missed revenue records...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_created = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")
            
            if not dry_run:
                created_count = create_missed_revenue_records(partner, start_date, end_date)
                total_created += created_count
                self.stdout.write(f"  Created {created_count} missed revenue records")
            else:
                # Count expected missed payments for active drivers only
                vehicles = Vehicle.objects.filter(
                    partner=partner,
                    driver__isnull=False,
                    status='active'
                )
                expected_missed = 0
                
                for vehicle in vehicles:
                    if not vehicle.driver:
                        continue
                        
                    payment_settings = PaymentSettings.objects.filter(
                        vehicle=vehicle
                    ).first() or PaymentSettings.objects.filter(
                        partner=partner,
                        vehicle__isnull=True
                    ).first()
                    
                    if payment_settings:
                        # Only count days that have passed
                        yesterday = timezone.now().date() - timedelta(days=1)
                        actual_end_date = min(end_date, yesterday)
                        
                        if start_date <= actual_end_date:
                            expected_dates = get_expected_payment_days(vehicle, start_date, actual_end_date)
                            for expected_date in expected_dates:
                                existing_revenue = Revenue.objects.filter(
                                    vehicle=vehicle,
                                    date=expected_date,
                                    deleted=False
                                ).first()
                                
                                if not existing_revenue:
                                    expected_missed += 1
                
                self.stdout.write(f"  Would create {expected_missed} missed revenue records")

        self.stdout.write(
            self.style.SUCCESS(f"Total missed revenue records created: {total_created}")
        )

    def update_payment_status(self, partner_id, start_date, end_date, dry_run):
        """Update payment status for revenue records"""
        self.stdout.write("Updating payment status...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_updated = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")
            
            # Get all revenue records for this partner in the date range
            revenues = Revenue.objects.filter(
                vehicle__partner=partner,
                date__gte=start_date,
                date__lte=end_date,
                deleted=False
            ).select_related('vehicle', 'driver')

            for revenue in revenues:
                try:
                    if not dry_run:
                        # Update payment status based on deadline
                        payment_settings = PaymentSettings.objects.filter(
                            vehicle=revenue.vehicle
                        ).first() or PaymentSettings.objects.filter(
                            partner=partner,
                            vehicle__isnull=True
                        ).first()
                        
                        if payment_settings:
                            from akuko_api.rating_utils import get_payment_deadline
                            deadline = get_payment_deadline(revenue.vehicle, revenue.date)
                            
                            if deadline:
                                # Check if payment was made before deadline
                                payment_time = timezone.now()  # This should be the actual payment time
                                if payment_time <= deadline:
                                    if revenue.status != 'on_time':
                                        revenue.status = 'on_time'
                                        revenue.save()
                                        total_updated += 1
                                else:
                                    if revenue.status != 'late':
                                        revenue.status = 'late'
                                        revenue.save()
                                        total_updated += 1
                    else:
                        self.stdout.write(f"  Would update payment status for {revenue.vehicle.registration_number} on {revenue.date}")
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error updating payment status for {revenue.vehicle.registration_number}: {str(e)}")
                    )

        self.stdout.write(
            self.style.SUCCESS(f"Total payment status updates: {total_updated}")
        )



    def cleanup_inactive_ratings(self, partner_id, dry_run):
        """Clean up inactive ratings (those with 3+ consecutive non-payment days)"""
        self.stdout.write("Cleaning up inactive ratings...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_cleaned = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")
            
            # Find ratings that should be inactive
            ratings = DriverRating.objects.filter(
                partner=partner,
                consecutive_non_payment_days__gte=3,
                is_active=True
            ).select_related('driver', 'vehicle')

            for rating in ratings:
                try:
                    if not dry_run:
                        rating.is_active = False
                        rating.save()
                        total_cleaned += 1
                        self.stdout.write(f"  Deactivated rating for {rating.driver.first_name} - {rating.vehicle.registration_number}")
                    else:
                        self.stdout.write(f"  Would deactivate rating for {rating.driver.first_name} - {rating.vehicle.registration_number}")
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"Error cleaning up rating for {rating.driver.first_name}: {str(e)}")
                    )

        self.stdout.write(
            self.style.SUCCESS(f"Total ratings cleaned up: {total_cleaned}")
        )

    def recalculate_all(self, partner_id, start_date, end_date, dry_run):
        """Recalculate all ratings and handle missed payments efficiently"""
        self.stdout.write("Performing comprehensive recalculation...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_processed = 0

        for partner in partners:
            self.stdout.write(f"Processing partner: {partner.first_name} {partner.last_name}")
            
            try:
                # Step 1: Create missed revenue records
                if not dry_run:
                    missed_created = create_missed_revenue_records(partner, start_date, end_date)
                    self.stdout.write(f"  Created {missed_created} missed revenue records")
                else:
                    self.stdout.write("  Would create missed revenue records")

                # Step 2: Update payment statuses
                revenues = Revenue.objects.filter(
                    vehicle__partner=partner,
                    date__gte=start_date,
                    date__lte=end_date,
                    deleted=False
                )
                
                status_updates = 0
                for revenue in revenues:
                    if not dry_run:
                        # Update status based on payment timing
                        payment_settings = PaymentSettings.objects.filter(
                            vehicle=revenue.vehicle
                        ).first() or PaymentSettings.objects.filter(
                            partner=partner,
                            vehicle__isnull=True
                        ).first()
                        
                        if payment_settings:
                            from akuko_api.rating_utils import get_payment_deadline
                            deadline = get_payment_deadline(revenue.vehicle, revenue.date)
                            
                            if deadline:
                                payment_time = timezone.now()  # Should be actual payment time
                                if payment_time <= deadline and revenue.status != 'on_time':
                                    revenue.status = 'on_time'
                                    revenue.save()
                                    status_updates += 1
                                elif payment_time > deadline and revenue.status not in ['late', 'missed']:
                                    revenue.status = 'late'
                                    revenue.save()
                                    status_updates += 1
                    else:
                        status_updates += 1

                self.stdout.write(f"  Updated {status_updates} payment statuses")

                # Step 3: Calculate ratings
                vehicles_with_drivers = Vehicle.objects.filter(
                    partner=partner,
                    driver__isnull=False,
                    status='active'
                ).select_related('driver')

                ratings_calculated = 0
                for vehicle in vehicles_with_drivers:
                    try:
                        if not dry_run:
                            calculate_driver_rating(vehicle.driver, vehicle, end_date, start_date)
                            ratings_calculated += 1
                        else:
                            ratings_calculated += 1
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"Error calculating rating for {vehicle.driver.first_name}: {str(e)}")
                        )

                self.stdout.write(f"  Calculated {ratings_calculated} ratings")
                total_processed += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error processing partner {partner.first_name} {partner.last_name}: {str(e)}")
                )

        self.stdout.write(
            self.style.SUCCESS(f"Completed comprehensive recalculation for {total_processed} partners")
        )

    def check_missed_payments(self, partner_id, start_date, end_date, dry_run):
        """Check for missed payments and provide summary"""
        self.stdout.write("Checking for missed payments...")
        
        # Get partners to process
        if partner_id:
            partners = Partner.objects.filter(id=partner_id)
        else:
            partners = Partner.objects.all()

        total_missed = 0
        total_late = 0
        total_on_time = 0

        for partner in partners:
            self.stdout.write(f"Checking partner: {partner.first_name} {partner.last_name}")
            
            # Get all revenue records for this partner in the date range
            revenues = Revenue.objects.filter(
                vehicle__partner=partner,
                date__gte=start_date,
                date__lte=end_date,
                deleted=False
            ).select_related('vehicle', 'driver')

            partner_missed = 0
            partner_late = 0
            partner_on_time = 0

            for revenue in revenues:
                if revenue.status == 'missed':
                    partner_missed += 1
                    total_missed += 1
                elif revenue.status == 'late':
                    partner_late += 1
                    total_late += 1
                elif revenue.status == 'on_time':
                    partner_on_time += 1
                    total_on_time += 1

            self.stdout.write(f"  Missed: {partner_missed}, Late: {partner_late}, On Time: {partner_on_time}")

        self.stdout.write(
            self.style.SUCCESS(f"Summary - Total Missed: {total_missed}, Late: {total_late}, On Time: {total_on_time}")
        ) 