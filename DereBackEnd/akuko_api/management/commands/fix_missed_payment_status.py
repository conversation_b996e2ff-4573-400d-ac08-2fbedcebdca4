from django.core.management.base import BaseCommand
from django.utils import timezone
from akuko_api.models import Revenue


class Command(BaseCommand):
    help = 'Fix missed payment records that have incorrect status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making changes (for testing)'
        )
        parser.add_argument(
            '--fix-all',
            action='store_true',
            help='Fix all missed payment records regardless of date'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        fix_all = options['fix_all']

        self.stdout.write("Fixing missed payment record statuses...")
        
        if dry_run:
            self.stdout.write("DRY RUN MODE - No changes will be made")

        # Find revenue records that are missed payments but have wrong status
        missed_payment_filter = Revenue.objects.filter(
            confirmation_message__icontains='Missed payment for',
            deleted=False
        )
        
        if not fix_all:
            # Only fix recent records (last 30 days)
            cutoff_date = timezone.now().date() - timezone.timedelta(days=30)
            missed_payment_filter = missed_payment_filter.filter(date__gte=cutoff_date)

        # Find records with wrong status
        wrong_status_records = missed_payment_filter.exclude(status='missed')

        total_records = wrong_status_records.count()
        self.stdout.write(f"Found {total_records} missed payment records with incorrect status")

        fixed_count = 0
        
        for record in wrong_status_records:
            current_status = record.status
            
            if not dry_run:
                record.status = 'missed'
                # Use update() to avoid triggering the save() method which might change status again
                Revenue.objects.filter(id=record.id).update(status='missed')
                fixed_count += 1
                
            self.stdout.write(
                f"Record {record.id}: {record.vehicle.registration_number} on {record.date} - "
                f"Changed from '{current_status}' to 'missed'"
            )

        if dry_run:
            self.stdout.write(f"Would fix {total_records} records")
        else:
            self.stdout.write(
                self.style.SUCCESS(f"Fixed {fixed_count} missed payment records")
            )

        # Show summary of current status distribution
        self.stdout.write("\nCurrent status distribution for missed payment records:")
        
        statuses = Revenue.objects.filter(
            confirmation_message__icontains='Missed payment for',
            deleted=False
        ).values_list('status', flat=True)
        
        status_counts = {}
        for status in statuses:
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in status_counts.items():
            self.stdout.write(f"  {status}: {count}")