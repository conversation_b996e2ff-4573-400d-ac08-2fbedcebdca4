# Historical Rating Calculation Guide

This guide walks you through calculating driver ratings from existing revenue data.

## 🎯 When to Use This Process

- You have existing revenue records without ratings
- You need to recalculate ratings from scratch
- You've imported historical payment data
- You want to verify rating accuracy

## 📋 Prerequisites

1. **Revenue Records**: Historical payment data exists
2. **Payment Settings**: Each vehicle has payment configuration
3. **Active Drivers**: Driver records are properly linked
4. **Vehicle Data**: Vehicle records exist and are linked to partners

## 🔄 Step-by-Step Process

### **Step 1: Analyze Your Data**

First, understand what data you have:

```bash
# Analyze existing data without making changes
python calculate_historical_ratings.py --analyze-only
```

This will show you:
- Total revenue records and date range
- Revenue breakdown by partner/driver/vehicle
- Payment settings status
- Current rating records

### **Step 2: Verify Payment Settings**

Ensure all vehicles have payment settings:

```bash
# Check payment settings for specific partner
python calculate_historical_ratings.py --partner-id 1 --analyze-only
```

**If missing payment settings:**
1. Create payment settings for each vehicle
2. Define payment days (e.g., "Monday,Wednesday,Friday")
3. Set daily amounts and deadline times

### **Step 3: Preview the Process (Dry Run)**

Always run a dry run first to see what will happen:

```bash
# Dry run for specific partner
python calculate_historical_ratings.py --partner-id 1 --dry-run

# Dry run for all partners
python calculate_historical_ratings.py --all-partners --dry-run
```

### **Step 4: Update Revenue Payment Statuses**

Update existing revenue records with proper payment statuses:

```bash
# Update statuses for specific partner
python calculate_historical_ratings.py --partner-id 1 --skip-missed-records

# Update statuses for all partners
python calculate_historical_ratings.py --all-partners --skip-missed-records
```

### **Step 5: Create Missing Revenue Records**

Create "missed" payment records for expected payment days:

```bash
# Create missed records for specific date range
python calculate_historical_ratings.py --partner-id 1 --start-date 2025-05-01 --end-date 2025-08-06 --skip-status-update

# Create missed records for last 90 days
python calculate_historical_ratings.py --partner-id 1 --skip-status-update
```

### **Step 6: Calculate All Ratings**

Finally, calculate ratings for all driver-vehicle combinations:

```bash
# Calculate ratings for specific partner
python calculate_historical_ratings.py --partner-id 1

# Calculate ratings for all partners from specific date
python calculate_historical_ratings.py --all-partners --start-date 2025-05-01

# Recalculate everything from scratch
python calculate_historical_ratings.py --partner-id 1 --recalculate-all
```

## 🚀 Quick Start for Your Data

Based on your current data (157 revenue records, Partner Ken Njenga), here's the recommended process:

### **Option A: Complete Process (Recommended)**
```bash
# 1. First, analyze your data
python calculate_historical_ratings.py --analyze-only

# 2. Run dry run to preview
python calculate_historical_ratings.py --partner-id 1 --start-date 2025-05-01 --dry-run

# 3. Execute the full process
python calculate_historical_ratings.py --partner-id 1 --start-date 2025-05-01
```

### **Option B: Step-by-Step Process**
```bash
# 1. Update revenue statuses only
python calculate_historical_ratings.py --partner-id 1 --skip-missed-records

# 2. Create missed payment records
python calculate_historical_ratings.py --partner-id 1 --start-date 2025-05-01 --skip-status-update

# 3. Calculate ratings
python calculate_historical_ratings.py --partner-id 1 --start-date 2025-05-01 --skip-status-update --skip-missed-records
```

### **Option C: Using Management Commands**
```bash
# Alternative using built-in management commands
python manage.py manage_driver_ratings --action full_sync --partner-id 1 --start-date 2025-05-01 --end-date 2025-08-06
```

## 📊 Expected Output

The process will show:

```
================================================================================
 Historical Rating Calculation
================================================================================
Started at: 2025-08-06 20:30:00

================================================================================
 Data Analysis
================================================================================
📊 Total Revenue Records: 157
📅 Date Range: 2025-05-02 to 2025-08-06

--- Revenue by Partner ---
  Partner 1: Ken Njenga - 157 records

--- Payment Settings Status ---
📋 Total Payment Settings: 2
🚗 Vehicles with Settings: 2/2

================================================================================
 Updating Revenue Payment Statuses
================================================================================
📊 Total records to process: 157
✅ Processing complete!
   Updated records: 45
   On-time: 89
   Late: 23
   Missed: 0

================================================================================
 Creating Missing Revenue Records
================================================================================
📅 Date range: 2025-05-01 to 2025-08-06
🏢 Partner: Ken Njenga
   Created 12 missed revenue records

================================================================================
 Calculating Driver Ratings
================================================================================
👥 Total drivers to process: 3
👤 Driver 1/3: John Doe
   🚗 Vehicle: KCA123X
      Creating new rating...
      Rating: 4.25 (127.5 points / 30 days)
      Status: Active
```

## ⚠️ Important Considerations

### **Data Backup**
Always backup your database before running:
```bash
pg_dump your_database > backup_before_rating_calculation.sql
```

### **Performance**
For large datasets:
- Process one partner at a time
- Use date ranges to limit scope
- Run during off-peak hours
- Monitor memory usage

### **Validation**
After calculation, verify results:
```bash
# Check rating distribution
python manage.py shell
>>> from akuko_api.models import DriverRating
>>> ratings = DriverRating.objects.all()
>>> print(f"Total ratings: {ratings.count()}")
>>> print(f"Average rating: {ratings.aggregate(avg=models.Avg('rating_score'))['avg']:.2f}")
```

### **Common Issues**

1. **Missing Payment Settings**
   - Create payment settings for all vehicles
   - Ensure payment days and times are set

2. **Revenue Status Issues**
   - Some revenue records may not have status set
   - The script will update these automatically

3. **Date Range Problems**
   - Ensure start_date is before your earliest revenue
   - End date should be today or earlier

4. **Memory Issues**
   - Process smaller date ranges
   - Use partner-specific processing

## 🔧 Troubleshooting

### **Script Fails**
```bash
# Check for errors in detail
python calculate_historical_ratings.py --partner-id 1 --dry-run --start-date 2025-05-01
```

### **Ratings Look Wrong**
```bash
# Recalculate specific driver
python manage.py shell
>>> from akuko_api.rating_utils import calculate_driver_rating
>>> from akuko_api.models import Driver, Vehicle
>>> driver = Driver.objects.get(id=1)
>>> vehicle = Vehicle.objects.get(id=1)
>>> rating = calculate_driver_rating(driver, vehicle)
>>> print(f"Rating: {rating.rating_score}")
```

### **Performance Issues**
```bash
# Process smaller chunks
python calculate_historical_ratings.py --partner-id 1 --start-date 2025-07-01 --end-date 2025-07-31
```

## 📈 Post-Calculation Steps

1. **Verify Results**: Check rating distribution and accuracy
2. **Test API Endpoints**: Ensure rating endpoints work correctly
3. **Update Frontend**: Refresh any cached rating data
4. **Monitor Performance**: Check if ratings update correctly with new payments
5. **Schedule Maintenance**: Set up regular rating calculations

## 🎯 Success Metrics

After completion, you should have:
- ✅ All driver-vehicle combinations have ratings
- ✅ Rating scores are between 0.0 and 5.0
- ✅ Payment statuses are properly set
- ✅ Missed payment records exist for expected days
- ✅ API endpoints return rating data correctly

This process will transform your historical revenue data into a comprehensive driver rating system!
