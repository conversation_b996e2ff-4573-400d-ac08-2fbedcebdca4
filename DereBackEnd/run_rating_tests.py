#!/usr/bin/env python
"""
Driver Rating System Test Runner

This script provides an easy way to run all driver rating tests with detailed output.
It can run individual test classes or all tests together.

Usage:
    python run_rating_tests.py                    # Run all rating tests
    python run_rating_tests.py --class calculation # Run only calculation tests
    python run_rating_tests.py --class commands    # Run only management command tests
    python run_rating_tests.py --class api         # Run only API endpoint tests
    python run_rating_tests.py --class permissions # Run only permission tests
    python run_rating_tests.py --class edge        # Run only edge case tests
    python run_rating_tests.py --class integration # Run only integration tests
    python run_rating_tests.py --verbose           # Run with verbose output
    python run_rating_tests.py --coverage          # Run with coverage report
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'akukoproject.settings')

# Test class mappings
TEST_CLASSES = {
    'calculation': 'akuko_api.tests.TestDriverRatingCalculation',
    'commands': 'akuko_api.tests.TestDriverRatingManagementCommands',
    'api': 'akuko_api.tests.TestDriverRatingAPIEndpoints',
    'permissions': 'akuko_api.tests.TestDriverRatingPermissions',
    'edge': 'akuko_api.tests.TestDriverRatingEdgeCases',
    'integration': 'akuko_api.tests.TestDriverRatingIntegration',
    'all': 'akuko_api.tests'
}

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 80)
    print(f" {title}")
    print("=" * 80)

def print_section(title):
    """Print a formatted section header"""
    print(f"\n--- {title} ---")

def run_command(cmd, description):
    """Run a command and return the result"""
    print(f"\n🔄 {description}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        if result.returncode == 0:
            print("✅ Success!")
            if result.stdout:
                print("Output:")
                print(result.stdout)
        else:
            print("❌ Failed!")
            if result.stderr:
                print("Error:")
                print(result.stderr)
            if result.stdout:
                print("Output:")
                print(result.stdout)
        
        return result.returncode == 0
    
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        return False

def run_tests(test_class=None, verbose=False, coverage=False):
    """Run the specified tests"""
    
    if test_class and test_class in TEST_CLASSES:
        test_target = TEST_CLASSES[test_class]
        title = f"Driver Rating Tests - {test_class.title()}"
    else:
        test_target = TEST_CLASSES['all']
        title = "Driver Rating Tests - All"
    
    print_header(title)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Test Target: {test_target}")
    
    # Build the test command
    if coverage:
        cmd = ['coverage', 'run', '--source=.', 'manage.py', 'test']
    else:
        cmd = ['python', 'manage.py', 'test']
    
    cmd.append(test_target)
    
    if verbose:
        cmd.extend(['--verbosity=2'])
    
    # Run the tests
    success = run_command(cmd, f"Running {title}")
    
    # If using coverage, generate report
    if coverage and success:
        print_section("Coverage Report")
        run_command(['coverage', 'report'], "Generating coverage report")
        run_command(['coverage', 'html'], "Generating HTML coverage report")
        print("📊 HTML coverage report generated in htmlcov/index.html")
    
    return success

def run_specific_test_examples():
    """Show examples of running specific tests"""
    print_header("Example: Running Specific Test Methods")
    
    examples = [
        {
            'description': 'Test fresh rating calculation with perfect payments',
            'command': ['python', 'manage.py', 'test', 
                       'akuko_api.tests.TestDriverRatingCalculation.test_fresh_rating_calculation_perfect_payments']
        },
        {
            'description': 'Test management command dry run',
            'command': ['python', 'manage.py', 'test',
                       'akuko_api.tests.TestDriverRatingManagementCommands.test_management_command_dry_run']
        },
        {
            'description': 'Test driver rating API endpoint',
            'command': ['python', 'manage.py', 'test',
                       'akuko_api.tests.TestDriverRatingAPIEndpoints.test_driver_rating_endpoint_as_driver']
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['description']}")
        print(f"Command: {' '.join(example['command'])}")

def validate_environment():
    """Validate that the environment is set up correctly"""
    print_header("Environment Validation")
    
    # Check if Django is available
    try:
        import django
        print(f"✅ Django version: {django.get_version()}")
    except ImportError:
        print("❌ Django not found")
        return False
    
    # Check if the project settings can be loaded
    try:
        import django
        django.setup()
        from django.conf import settings
        print(f"✅ Django settings loaded: {settings.SETTINGS_MODULE}")
    except Exception as e:
        print(f"❌ Failed to load Django settings: {str(e)}")
        return False
    
    # Check if test database can be created
    try:
        from django.test.utils import setup_test_environment, teardown_test_environment
        setup_test_environment()
        print("✅ Test environment setup successful")
        teardown_test_environment()
    except Exception as e:
        print(f"❌ Test environment setup failed: {str(e)}")
        return False
    
    # Check if required models are available
    try:
        from akuko_api.models import Driver, DriverRating, Revenue, PaymentSettings
        print("✅ Required models are available")
    except ImportError as e:
        print(f"❌ Required models not found: {str(e)}")
        return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Driver Rating System Test Runner',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        '--class',
        dest='test_class',
        choices=list(TEST_CLASSES.keys()),
        help='Specific test class to run'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Run tests with verbose output'
    )
    
    parser.add_argument(
        '--coverage',
        action='store_true',
        help='Run tests with coverage report'
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='Only validate environment setup'
    )
    
    parser.add_argument(
        '--examples',
        action='store_true',
        help='Show examples of running specific tests'
    )
    
    args = parser.parse_args()
    
    # Validate environment first
    if not validate_environment():
        print("\n❌ Environment validation failed. Please fix the issues above.")
        return 1
    
    if args.validate:
        print("\n✅ Environment validation passed!")
        return 0
    
    if args.examples:
        run_specific_test_examples()
        return 0
    
    # Run the tests
    success = run_tests(
        test_class=args.test_class,
        verbose=args.verbose,
        coverage=args.coverage
    )
    
    if success:
        print_header("Test Results Summary")
        print("✅ All tests passed successfully!")
        print("\n📚 For more information, see:")
        print("   - DRIVER_RATING_TESTING_GUIDE.md")
        print("   - Individual test files in akuko_api/tests.py")
        
        if args.coverage:
            print("   - Coverage report in htmlcov/index.html")
        
        return 0
    else:
        print_header("Test Results Summary")
        print("❌ Some tests failed!")
        print("\n🔍 Troubleshooting tips:")
        print("   - Check the error messages above")
        print("   - Ensure database is properly set up")
        print("   - Verify all required dependencies are installed")
        print("   - See DRIVER_RATING_TESTING_GUIDE.md for more help")
        
        return 1

if __name__ == '__main__':
    sys.exit(main())
