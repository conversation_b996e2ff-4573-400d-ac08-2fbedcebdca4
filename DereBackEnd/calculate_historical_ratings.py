#!/usr/bin/env python
"""
Historical Rating Calculation Script

This script calculates driver ratings from existing revenue data.
Use this when you have historical revenue records and need to generate ratings.

Usage:
    python calculate_historical_ratings.py --partner-id 1 --start-date 2025-05-01
    python calculate_historical_ratings.py --all-partners --dry-run
    python calculate_historical_ratings.py --partner-id 1 --recalculate-all
"""

import os
import sys
import django
from datetime import datetime, timedelta
import argparse

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'akukoproject.settings')
django.setup()

from django.utils import timezone
from django.db.models import Count, Min, Max, Q
from akuko_api.models import Revenue, PaymentSettings, DriverRating, Partner, Driver, Vehicle
from akuko_api.rating_utils import (
    calculate_driver_rating, create_missed_revenue_records,
    create_or_update_driver_rating, update_payment_status_for_revenue
)

def print_header(title):
    """Print formatted header"""
    print("\n" + "=" * 80)
    print(f" {title}")
    print("=" * 80)

def print_section(title):
    """Print formatted section"""
    print(f"\n--- {title} ---")

def analyze_existing_data():
    """Analyze existing revenue data"""
    print_header("Data Analysis")
    
    # Revenue data analysis
    total_revenue = Revenue.objects.filter(deleted=False).count()
    date_range = Revenue.objects.filter(deleted=False).aggregate(
        earliest=Min('date'),
        latest=Max('date')
    )
    
    print(f"📊 Total Revenue Records: {total_revenue}")
    print(f"📅 Date Range: {date_range['earliest']} to {date_range['latest']}")
    
    # Partner breakdown
    print_section("Revenue by Partner")
    partner_revenue = Revenue.objects.filter(deleted=False).values(
        'vehicle__partner__id',
        'vehicle__partner__first_name', 
        'vehicle__partner__last_name'
    ).annotate(count=Count('id')).order_by('-count')
    
    for pr in partner_revenue:
        partner_name = f"{pr['vehicle__partner__first_name']} {pr['vehicle__partner__last_name']}"
        print(f"  Partner {pr['vehicle__partner__id']}: {partner_name} - {pr['count']} records")
    
    # Driver breakdown
    print_section("Revenue by Driver")
    driver_revenue = Revenue.objects.filter(deleted=False).values(
        'driver__id',
        'driver__first_name',
        'driver__last_name'
    ).annotate(count=Count('id')).order_by('-count')
    
    for dr in driver_revenue[:10]:  # Top 10
        driver_name = f"{dr['driver__first_name']} {dr['driver__last_name']}"
        print(f"  Driver {dr['driver__id']}: {driver_name} - {dr['count']} records")
    
    # Vehicle breakdown
    print_section("Revenue by Vehicle")
    vehicle_revenue = Revenue.objects.filter(deleted=False).values(
        'vehicle__id',
        'vehicle__registration_number'
    ).annotate(count=Count('id')).order_by('-count')
    
    for vr in vehicle_revenue[:10]:  # Top 10
        print(f"  Vehicle {vr['vehicle__id']}: {vr['vehicle__registration_number']} - {vr['count']} records")
    
    # Payment settings check
    print_section("Payment Settings Status")
    payment_settings_count = PaymentSettings.objects.count()
    print(f"📋 Total Payment Settings: {payment_settings_count}")
    
    vehicles_with_settings = PaymentSettings.objects.values('vehicle__id').distinct().count()
    total_vehicles = Vehicle.objects.filter(status='active').count()
    print(f"🚗 Vehicles with Settings: {vehicles_with_settings}/{total_vehicles}")
    
    if vehicles_with_settings < total_vehicles:
        print("⚠️  Warning: Some vehicles don't have payment settings!")
        vehicles_without_settings = Vehicle.objects.filter(
            status='active'
        ).exclude(
            id__in=PaymentSettings.objects.values('vehicle__id')
        )
        for vehicle in vehicles_without_settings[:5]:
            print(f"    Missing: {vehicle.registration_number} (ID: {vehicle.id})")
    
    # Current ratings check
    print_section("Current Rating Status")
    current_ratings = DriverRating.objects.count()
    print(f"⭐ Current Rating Records: {current_ratings}")
    
    return {
        'total_revenue': total_revenue,
        'date_range': date_range,
        'partners': list(partner_revenue),
        'payment_settings_count': payment_settings_count,
        'current_ratings': current_ratings
    }

def verify_payment_settings(partner_id=None):
    """Verify and show payment settings"""
    print_header("Payment Settings Verification")
    
    if partner_id:
        settings = PaymentSettings.objects.filter(partner_id=partner_id)
        print(f"Payment settings for Partner ID {partner_id}:")
    else:
        settings = PaymentSettings.objects.all()
        print("All payment settings:")
    
    for setting in settings:
        print(f"\n🚗 Vehicle: {setting.vehicle.registration_number}")
        print(f"   Partner: {setting.partner.first_name} {setting.partner.last_name}")
        print(f"   Daily Amount: KSh {setting.daily_amount}")
        print(f"   Payment Days: {setting.payment_days}")
        print(f"   Deadline Time: {setting.deadline_time}")
    
    if not settings.exists():
        print("❌ No payment settings found!")
        return False
    
    return True

def update_revenue_statuses(partner_id=None, dry_run=False):
    """Update payment statuses for existing revenue records"""
    print_header("Updating Revenue Payment Statuses")
    
    if partner_id:
        revenues = Revenue.objects.filter(
            vehicle__partner_id=partner_id,
            deleted=False
        ).order_by('date')
        print(f"Processing revenue records for Partner ID {partner_id}")
    else:
        revenues = Revenue.objects.filter(deleted=False).order_by('date')
        print("Processing all revenue records")
    
    total_records = revenues.count()
    print(f"📊 Total records to process: {total_records}")
    
    if dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
    
    updated_count = 0
    status_counts = {'on_time': 0, 'late': 0, 'missed': 0, 'unchanged': 0}
    
    for i, revenue in enumerate(revenues, 1):
        if i % 50 == 0:
            print(f"   Processed {i}/{total_records} records...")
        
        old_status = revenue.status
        
        if not dry_run:
            new_status = update_payment_status_for_revenue(revenue)
        else:
            # Simulate status update for dry run
            new_status = old_status or 'on_time'
        
        if old_status != new_status:
            updated_count += 1
            status_counts[new_status] += 1
        else:
            status_counts['unchanged'] += 1
    
    print(f"\n✅ Processing complete!")
    print(f"   Updated records: {updated_count}")
    print(f"   On-time: {status_counts['on_time']}")
    print(f"   Late: {status_counts['late']}")
    print(f"   Missed: {status_counts['missed']}")
    print(f"   Unchanged: {status_counts['unchanged']}")
    
    return updated_count

def create_missing_revenue_records(partner_id=None, start_date=None, end_date=None, dry_run=False):
    """Create missed revenue records for expected payment days"""
    print_header("Creating Missing Revenue Records")
    
    if not start_date:
        # Use earliest revenue date
        earliest_revenue = Revenue.objects.filter(deleted=False).aggregate(Min('date'))['date__min']
        start_date = earliest_revenue or timezone.now().date() - timedelta(days=90)
    
    if not end_date:
        end_date = timezone.now().date()
    
    print(f"📅 Date range: {start_date} to {end_date}")
    
    if partner_id:
        partners = Partner.objects.filter(id=partner_id)
        print(f"Processing Partner ID {partner_id}")
    else:
        partners = Partner.objects.all()
        print("Processing all partners")
    
    total_created = 0
    
    for partner in partners:
        print(f"\n🏢 Partner: {partner.first_name} {partner.last_name}")
        
        if dry_run:
            print("   🔍 DRY RUN MODE - No records will be created")
            # Simulate creation count
            created_count = 0
        else:
            created_count = create_missed_revenue_records(partner, start_date, end_date)
        
        print(f"   Created {created_count} missed revenue records")
        total_created += created_count
    
    print(f"\n✅ Total missed revenue records created: {total_created}")
    return total_created

def calculate_all_ratings(partner_id=None, start_date=None, dry_run=False):
    """Calculate ratings for all driver-vehicle combinations"""
    print_header("Calculating Driver Ratings")
    
    if partner_id:
        drivers = Driver.objects.filter(partner_id=partner_id)
        print(f"Processing drivers for Partner ID {partner_id}")
    else:
        drivers = Driver.objects.all()
        print("Processing all drivers")
    
    total_drivers = drivers.count()
    print(f"👥 Total drivers to process: {total_drivers}")
    
    if dry_run:
        print("🔍 DRY RUN MODE - No ratings will be calculated")
    
    processed_ratings = 0
    created_ratings = 0
    updated_ratings = 0
    
    for i, driver in enumerate(drivers, 1):
        print(f"\n👤 Driver {i}/{total_drivers}: {driver.first_name} {driver.last_name}")
        
        # Get all vehicles this driver has revenue records for
        vehicles_used = Revenue.objects.filter(
            driver=driver,
            deleted=False
        ).values_list('vehicle', flat=True).distinct()
        
        driver_ratings = 0
        
        for vehicle_id in vehicles_used:
            try:
                vehicle = Vehicle.objects.get(id=vehicle_id)
                print(f"   🚗 Vehicle: {vehicle.registration_number}")
                
                if not dry_run:
                    # Check if rating exists
                    existing_rating = DriverRating.objects.filter(
                        driver=driver,
                        vehicle=vehicle
                    ).first()
                    
                    if existing_rating:
                        print(f"      Updating existing rating...")
                        updated_ratings += 1
                    else:
                        print(f"      Creating new rating...")
                        created_ratings += 1
                    
                    # Calculate rating
                    rating = calculate_driver_rating(
                        driver=driver,
                        vehicle=vehicle,
                        calculation_date=timezone.now().date(),
                        start_date=start_date
                    )
                    
                    print(f"      Rating: {rating.rating_score:.2f} ({rating.total_points:.1f} points / {rating.total_payment_days} days)")
                    print(f"      Status: {'Active' if rating.is_active else 'Inactive'}")
                else:
                    print(f"      Would calculate rating for this vehicle")
                
                driver_ratings += 1
                processed_ratings += 1
                
            except Vehicle.DoesNotExist:
                print(f"      ❌ Vehicle ID {vehicle_id} not found")
                continue
        
        print(f"   Processed {driver_ratings} vehicle ratings for this driver")
    
    print(f"\n✅ Rating calculation complete!")
    print(f"   Total ratings processed: {processed_ratings}")
    print(f"   New ratings created: {created_ratings}")
    print(f"   Existing ratings updated: {updated_ratings}")
    
    return processed_ratings

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Calculate historical driver ratings')
    parser.add_argument('--partner-id', type=int, help='Process specific partner only')
    parser.add_argument('--all-partners', action='store_true', help='Process all partners')
    parser.add_argument('--start-date', type=str, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='End date (YYYY-MM-DD)')
    parser.add_argument('--dry-run', action='store_true', help='Preview changes without executing')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze data, don\'t process')
    parser.add_argument('--skip-status-update', action='store_true', help='Skip revenue status updates')
    parser.add_argument('--skip-missed-records', action='store_true', help='Skip creating missed records')
    parser.add_argument('--recalculate-all', action='store_true', help='Recalculate all ratings from scratch')
    
    args = parser.parse_args()
    
    # Parse dates
    start_date = None
    end_date = None
    
    if args.start_date:
        try:
            start_date = datetime.strptime(args.start_date, '%Y-%m-%d').date()
        except ValueError:
            print("❌ Invalid start date format. Use YYYY-MM-DD")
            return 1
    
    if args.end_date:
        try:
            end_date = datetime.strptime(args.end_date, '%Y-%m-%d').date()
        except ValueError:
            print("❌ Invalid end date format. Use YYYY-MM-DD")
            return 1
    
    print_header("Historical Rating Calculation")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
    
    # Step 1: Analyze existing data
    data_analysis = analyze_existing_data()
    
    if args.analyze_only:
        print("\n✅ Analysis complete!")
        return 0
    
    # Step 2: Verify payment settings
    if not verify_payment_settings(args.partner_id):
        print("❌ Cannot proceed without payment settings!")
        return 1
    
    # Step 3: Update revenue statuses (if not skipped)
    if not args.skip_status_update:
        update_revenue_statuses(args.partner_id, args.dry_run)
    
    # Step 4: Create missed revenue records (if not skipped)
    if not args.skip_missed_records:
        create_missing_revenue_records(
            partner_id=args.partner_id,
            start_date=start_date,
            end_date=end_date,
            dry_run=args.dry_run
        )
    
    # Step 5: Calculate ratings
    calculate_all_ratings(
        partner_id=args.partner_id,
        start_date=start_date,
        dry_run=args.dry_run
    )
    
    print_header("Process Complete")
    print(f"Finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("✅ Historical rating calculation completed successfully!")
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
